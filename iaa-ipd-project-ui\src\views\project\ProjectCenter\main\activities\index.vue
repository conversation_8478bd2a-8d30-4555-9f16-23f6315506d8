<template>
  <ActivitiesContainer
    :basicsInfo="props.basicsInfo!"
    v-model:nodeList="baseNode"
    v-model:currentNode="currentNode"
    :stage-count="stageCount"
    @add="showDecompositionActivities"
    v-loading="loading"
    title="活动"
  >
    <template #activities-buttons>
      <vxe-toolbar
        size="mini"
        custom
        :export="hasPermission('activities_export')"
        class="w-100%"
        ref="activitiesToolbarRef"
      >
        <template #buttons>
          <el-button
            type="primary"
            plain
            @click="showBatchDecompositionActivities"
            v-if="hasPermission('activities_disassemble', props.basicsInfo?.id)"
          >
            分解模板活动
          </el-button>
          <el-button
            type="warning"
            plain
            v-if="props.custom && hasPermission('activities_disassemble', props.basicsInfo?.id)"
            @click="showDecompositionActivities"
          >
            自定义活动
          </el-button>
        </template>
      </vxe-toolbar>
    </template>
    <template #activities-table>
      <ActivitiesTable
        ref="activitiesTableRef"
        :list="activitiesList"
        :userList="userList"
        :basicsId="basicsInfo?.id"
        v-model:toolbar="activitiesToolbarRef"
        @show="showRow"
        @success="
          (item) => {
            listActivities()
            setCurrentRow(item)
          }
        "
      />
    </template>
  </ActivitiesContainer>
  <ActivitiesForm
    ref="activitiesFormRef"
    :userList="userList"
    :activities-list="activitiesList"
    :file-template-list="fileTemplateList"
    @success="listActivities"
    @refresh="listActivities"
  />
  <ActivitiesBatchForm
    ref="activitiesBatchFormRef"
    :basicsId="basicsInfo?.id!"
    :user-list="userList"
    :template-category="templateCategory"
    @success="listActivities"
  />
  <ActivitiesCustomForm
    ref="activitiesCustomFormRef"
    :user-list="userList"
    :basicsId="props.basicsInfo?.id"
    :stage="currentNode"
  />
</template>

<script lang="ts" setup>
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import ActivitiesTable from './ActivitiesTable.vue'
import { BasicsVO } from '@/api/project/basics'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import ActivitiesForm from './ActivitiesForm.vue'
import ActivitiesBatchForm from './ActivitiesBatchForm.vue'
import ActivitiesCustomForm from './ActivitiesCustomForm.vue'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { ActivitiesApi, ActivitiesVO } from '@/api/project/activities'
import { FileTemplateApi } from '@/api/project/file/template'
import { debounce } from 'min-dash'
import { useCache } from '@/hooks/web/useCache'
import { hasPermission } from '@/views/project/ProjectCenter/main/util/permission'

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  },
  custom: {
    type: Boolean,
    default: false
  },
  templateCategory: {
    type: String,
    default: ''
  }
})
const { wsCache } = useCache()
const baseNode = ref<any[]>([]) //项目基础节点列表
const currentNode = ref<number>(undefined as unknown as number) //当前节点
const activitiesCustomFormRef = ref()
const activitiesBatchFormRef = ref()
const userList = ref<UserVO[]>([])
const activitiesList = ref<ActivitiesVO[]>([])
const fileTemplateList = ref<any[]>([])
const loading = ref(false)
const activitiesTableRef = ref()
const activitiesToolbarRef = ref()
const activitiesFormRef = ref()
const queryParams = ref({
  basicsId: props.basicsInfo?.id,
  stage: currentNode.value
})
const stageCount = ref<any>({})

/** 获取活动列表 */
const listActivities = async () => {
  loading.value = true
  try {
    queryParams.value.basicsId = props.basicsInfo?.id
    const form = wsCache.get('project_page_show_form')
    if (form && form?.stage) {
      currentNode.value = Number(form.stage)
    }
    queryParams.value.stage = currentNode.value
    const res = await ActivitiesApi.getActivitiesList(queryParams.value)
    activitiesList.value = res
    if (form && form?.id) {
      await nextTick()
      unref(activitiesTableRef).setCurrent(Number(form.id))
    }
    if (form && form?.type) {
      wsCache.delete('project_page_show_form')
    }
  } finally {
    loading.value = false
  }
}

const getStageCount = async () => {
  const res = await ActivitiesApi.getStageCount(props.basicsInfo!.id!)
  stageCount.value = res
}

const listFileTemplate = async () => {
  const res = await FileTemplateApi.getFileTemplatePage({})
  fileTemplateList.value = res
}

/** 获取项目基础节点 */
const onListBaseNode = debounce(async () => {
  baseNode.value = []
  const res = await ActivitiesTemplateApi.listActivitiesTemplate({
    parentId: 0,
    categoryId: props.templateCategory,
    basicsId: props.basicsInfo?.id
  })
  if (res.length > 0) {
    currentNode.value = res[0].id
  }
  for (const item of res) {
    baseNode.value.push({ id: item.id, label: item.name })
  }
}, 100)

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

/** 显示活动分解页面 */
const showDecompositionActivities = async () => {
  const node = baseNode.value.find((item) => item.id === currentNode.value)
  unref(activitiesCustomFormRef)?.openForm(node.label)
}

const showBatchDecompositionActivities = async () => {
  const node = baseNode.value.find((item) => item.id === currentNode.value)
  unref(activitiesBatchFormRef)?.openForm(
    node.label,
    node.id,
    props.basicsInfo?.teams,
    props.basicsInfo?.releaseDate
  )
}

const showRow = async (row: ActivitiesVO) => {
  unref(activitiesFormRef)?.openForm(row, props.basicsInfo)
}

const setCurrentRow = async (id: number) => {
  // await listActivities()
  await nextTick()
  unref(activitiesTableRef)?.setCurrent(id)
}

watch(
  () => [props.basicsInfo, props.templateCategory],
  async () => {
    if (props.basicsInfo?.id && props.templateCategory) {
      baseNode.value = []
      await onListBaseNode()
    }
  },
  { immediate: true }
)
const debouncedFetch = debounce(() => {
  getStageCount()
  listActivities()
}, 300)

watch(
  () => [props.basicsInfo, currentNode.value],
  async () => {
    if (props.basicsInfo?.id && currentNode.value) {
      await debouncedFetch()
    }
  }
)
onMounted(() => {
  getUserList()
  listFileTemplate()
})
</script>
