<template>
  <el-form label-width="90px" ref="formRef" class="custom-form">
    <el-form-item label="原因">
      <div class="text-1rem bg-amber w-full rounded-1 break-all">
        {{
          formData.modifyInfo
            ?.filter((item) => item.modifyField === 'update')
            ?.map((item) => item.modifyFieldName)
            ?.join('\n')
        }}
      </div>
    </el-form-item>
    <el-form-item label="所属项目">
      <el-input v-model="formData.basicsName" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="等级">
          <el-input v-model="formData.basicsLevel" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="类型">
          <el-input v-model="formData.basicsMold" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="平台">
          <el-input v-model="formData.basicsPlatform" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="描述" prop="content">
      <el-input type="textarea" :rows="4" v-model="formData.content" :disabled="true" />
    </el-form-item>
    <el-form-item label="图片" prop="imgIds">
      <UploadImgs v-model="formData.imgIds!" height="60px" width="60px" :disabled="true" />
    </el-form-item>

    <el-row>
      <el-col :span="8">
        <el-form-item label="等级" prop="level">
          <el-select v-model="formData.level" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="分类" prop="category">
          <el-select v-model="formData.category" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_category')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="责任模块" prop="module">
          <el-select v-model="formData.module" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_module')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="8">
        <el-form-item label="提出部门" prop="proposingDepartment">
          <el-select v-model="formData.proposingDepartment" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_problem_proposing_department')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="阶段" prop="stage">
          <el-select v-model="formData.stage" :disabled="true">
            <el-option
              v-for="item in templateNode"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="不良比例(%)" prop="rejectRatio">
          <el-input-number
            v-model="formData.rejectRatio"
            :min="1"
            :max="100"
            :disabled="true"
            class="!w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="8">
        <el-form-item label="状态">
          <DictTag type="project_activities_status" :value="formData.status!" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="提出时间" prop="timeOfProposal">
          {{ formatDate(formData.timeOfProposal, 'YYYY-MM-DD') }}
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="计划完成" prop="timeOfPlan">
          {{ formatDate(formData.timeOfPlan, 'YYYY-MM-DD') }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="责任人" prop="director">
      <user-avatar-list
        v-model="formData.director!"
        :user-list="userList"
        :size="28"
        :limit="10"
        :add="false"
      />
    </el-form-item>
    <el-form-item label="执行人">
      <user-avatar-list
        v-model="formData.coordinate!"
        :user-list="userList"
        :size="28"
        :limit="10"
        :add="false"
      />
    </el-form-item>
    <el-form-item label="进度" v-if="formData.id">
      <el-progress
        :percentage="formData.progress"
        class="w-100% no-radius"
        :text-inside="true"
        :stroke-width="20"
        status="success"
      />
    </el-form-item>
    <el-form-item label="原因分析" prop="reason">
      <el-input type="textarea" :rows="4" v-model="formData.reason" :disabled="true" />
    </el-form-item>

    <el-form-item label="解决措施" prop="measures">
      <el-input type="textarea" :rows="4" v-model="formData.measures" :disabled="true" />
    </el-form-item>
    <el-form-item label="修改信息">
      <vxe-table
        :data="formData.modifyInfo?.filter((item) => item.modifyField !== 'update')"
        show-overflow
        :header-cell-style="{ padding: 0, fontSize: '1rem' }"
        :cell-style="{ padding: '5px', fontSize: '1rem', height: '1.5vw' }"
        border
        stripe
        align="center"
        class="!w-full"
      >
        <vxe-column title="修改字段/原因" field="modifyFieldName" />
        <!-- 统一处理列的渲染逻辑 -->
        <template v-for="col in ['beforeValue', 'afterValue']" :key="col">
          <vxe-column :title="col === 'beforeValue' ? '修改前' : '修改后'">
            <template #default="{ row }">
              <template v-if="shouldShowDictTag(row.modifyField)">
                <el-tag>
                  {{ formatDictLabel(row.modifyField, row[col]) }}
                </el-tag>
              </template>
              <template
                v-else-if="
                  ['managers', 'director', 'coordinate','proposal'].includes(row.modifyField) ||
                  getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                    (dict) => dict.value === row.modifyField
                  )?.label
                "
              >
                {{ getUserNickName(userList, row[col]) }}
              </template>
              <!-- <template v-else-if="row.modifyField === 'categoryIds'">
                {{ getCategoryName(row[col]) }}
              </template> -->
              <template v-else>{{ row[col] }}</template>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { ProblemFlowApi } from '@/api/bpm/problem'
import {
  shouldShowDictTag,
  getUserNickName,
  formatDictLabel
} from '@/views/project/ProjectCenter/details/components/utils'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import { formatDate } from '@/utils/formatTime'

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})

interface Node {
  id: number | string
  label: string
}

const templateNode = ref<Node[]>([])
const formData = ref<any>({})
const userList = ref<UserVO[]>([])

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

const init = async () => {
  const res = await ProblemFlowApi.getProblem(props.processInstanceId)
  formData.value = res
  templateNode.value = []
  const res1 = await ActivitiesTemplateApi.listActivitiesTemplate({
    parentId: 0,
    categoryId: res.templateCategory
  })
  for (const item of res1) {
    templateNode.value.push({ id: item.id, label: item.name })
  }
}

watch(
  () => props.processInstanceId,
  () => {
    if (props.processInstanceId) {
      init()
    }
  },
  { immediate: true }
)

onMounted(() => {
  getUserList()
})
</script>
