<template>
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      style="margin-top: 20px;"
    >
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="机会点" prop="opportunityPoints">
            <el-input v-model="formData.opportunityPoints" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提出人" prop="proposer">
            <el-input v-model="formData.proposer" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源" prop="source">
            <el-input v-model="formData.source" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="可应用的型号" prop="models">
            <TagsInput v-model="formData.models" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="拓展的专利点" prop="patentPoint">
            <TagsInput v-model="formData.patentPoint" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利布局" prop="patentLayout">
            <el-input v-model="formData.patentLayout" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="保护整机类型" prop="machineType">
            <el-input v-model="formData.machineType" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="组件" prop="component">
            <el-input v-model="formData.component" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="零件" prop="part">
            <el-input v-model="formData.part" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="保护的地区" prop="area">
            <el-input v-model="formData.area" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请号" prop="applicationNo">
            <el-input v-model="formData.applicationNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护点" prop="protectionPoint">
            <el-input v-model="formData.protectionPoint" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="附件" prop="attachmentIds">
            <UploadFile
              v-model="formData.attachmentIds"
              :limit="1"
              :file-size="100"
              class="min-w-80px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发明人" prop="inventor">
            <el-input v-model="formData.inventor" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际发明人" prop="actualInventor">
            <el-input v-model="formData.actualInventor" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { MiningLayoutApi } from '@/api/patent/miningLayout'
import UploadFile from '@/components/UploadFile/src/UploadFile.vue'
import TagsInput from '../components/TagsInput.vue'
const visible = ref(false)
const formData = ref({
  id: undefined,
  opportunityPoints: undefined,
  proposer: undefined,
  source: undefined,
  models:  [] as string[],
  patentPoint:  [] as string[],
  patentLayout: undefined,
  machineType: undefined,
  component: undefined,
  part: undefined,
  area: undefined,
  applicationNo: undefined,
  protectionPoint: undefined,
  attachmentIds:  [] as string[],
  inventor: undefined,
  actualInventor: undefined
})
const formRules = reactive({
  opportunityPoints: [{ required: true, message: '创新点或机会点', trigger: 'blur' }],
  proposer: [{ required: true, message: '请输入提出人', trigger: 'blur' }],
  applicationNo: [{ required: true, message: '请输入申请号', trigger: 'blur' }],
  models: [{ required: true, message: '请输入可应用的型号', trigger: 'blur' }],
  patentPoint: [{ required: true, message: '请输入可拓展的专利点', trigger: 'blur' }],
  area: [{ required: true, message: '请输入保护的地区', trigger: 'blur' }],
  protectionPoint: [{ required: true, message: '请输入保护点', trigger: 'blur' }],
})


// 抽屉的标题
const dialogTitle = ref('')
const formType = ref('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const databaseDialog = ref(false)

const openForm = async(rows?: any[] | any) => {
  visible.value = true
  if (rows) {
    formType.value = 'update'
    dialogTitle.value = '修改知识产权挖掘与布局'
    const data = await MiningLayoutApi.getMiningLayout(rows)
    formData.value = {
      ...data
    }
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加知识产权挖掘与布局'
    resetForm()
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
  id: undefined,
  opportunityPoints: undefined,
  proposer:  undefined,
  source: undefined,
  models:  [] as string[],
  patentPoint:  [] as string[],
  patentLayout: undefined,
  machineType: undefined,
  component: undefined,
  part: undefined,
  area: undefined,
  applicationNo: undefined,
  protectionPoint: undefined,
  attachmentIds:  [] as string[],
  inventor: undefined,
  actualInventor: undefined
  }
}
const emit = defineEmits(['success']) 
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  console.log(formData.value)
  try {
    if (formType.value === 'update') {
      await MiningLayoutApi.updateMiningLayout(formData.value)
      message.success('修改成功')
    } else {
      await MiningLayoutApi.createMiningLayout(formData.value)
      message.success('创建成功')
    }
    visible.value = false
      emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
