<template>
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      style="margin-top: 20px"
      class="custom-form"
      size="small"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="公开号" prop="publicNo">
            <el-input v-model="formData.publicNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利名称" prop="patentName">
            <el-input v-model="formData.patentName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利类型" prop="patentType">
            <el-select
              v-model="formData.patentType"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请状态" prop="status">
              <el-select
              v-model="formData.status"
              placeholder="请选择专利申请状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_MAINTENANCE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请号" prop="applicationNo">
            <el-input v-model="formData.applicationNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请日期" prop="applicationDate">
            <el-date-picker
              v-model="formData.applicationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护技术点" prop="protectionPoint">
            <el-input v-model="formData.protectionPoint" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护产品型号" prop="models">
            <TagsInput v-model="formData.models" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="获取方式" prop="acquisitionMethod">
            <el-input v-model="formData.acquisitionMethod" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="授权日期" prop="authorizationDate">
            <el-date-picker
              v-model="formData.authorizationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="有效期(年数)" prop="validityPeriod">
            <el-input v-model="formData.validityPeriod" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法律状态" prop="legalStatus">
            <el-select
              v-model="formData.legalStatus"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="当前权力人" prop="ownership">
            <el-input v-model="formData.ownership" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="维保评估" prop="maintenanceAssessment">
            <el-input
              v-model="formData.maintenanceAssessment"
              type="textarea"
              :rows="5"
              placeholder="请输入维保评估"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-tabs
        v-model="currentTab"
        class="position-sticky top-0 z-10 bg-#fff"
        @tab-change="onTableChange"
      >
        <el-tab-pane label="著录项信息" name="inventor" />
        <el-tab-pane label="申请文件" name="attachment" />
      </el-tabs>
      <div v-show="currentTab === 'inventor'">
        <InvertorInfo ref="inventorInfoRef" :initial-data="inventorData" :mode="formType" />
      </div>
      <div v-show="currentTab === 'attachment'">
        <AttachmentInfo ref="attachmentRef" :initial-data="inventorData" :mode="formType" />
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { MaintenanceApi } from '@/api/patent/maintenance'
import { SubclassApi } from '@/api/patent/subclass'
import InvertorInfo from './components/InvertorInfo.vue'
import AttachmentInfo from '../components/AttachmentInfo.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import TagsInput from '../components/TagsInput.vue'
const visible = ref(false)
const formData = ref({
  id: undefined,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  protectionPoint: undefined,
  attachmentIds: [] as string[],
  models: [] as string[],
  patentType: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  validityPeriod: undefined,
  inventor: undefined,
  actualInventor: undefined,
  acquisitionMethod: undefined,
  legalStatus: 0,
  agency: undefined,
  families: undefined,
  status: 0,
  maintenanceAssessment: undefined
})

const formRules = reactive({
  applicationNo: [{ required: true, message: '请输入申请号', trigger: 'blur' }],
  patentName: [{ required: true, message: '请输入专利名称', trigger: 'blur' }],
  patentType: [{ required: true, message: '请选择专利类型', trigger: 'blur' }],
  status: [{ required: true, message: '请选择申请状态', trigger: 'blur' }],
  protectionPoint: [{ required: true, message: '请输入保护点', trigger: 'blur' }]
})

const inventorData = ref({
  inventorList: [
    // 初始化发明人列表
  ],
  applicationList: [],
  agencyList: [
    // 初始化代理机构列表
  ],
  attachmentList: [
    // 初始化附件列表
  ]
})
const inventorInfoRef = ref()
const attachmentRef = ref()

/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'inventor':
      inventorInfoRef.value?.refreshComment()
      break
    case 'attachment':
      attachmentRef.value?.onListAttachment()
      break
  }
}

// 默认选中著录项信息
const currentTab = ref('inventor')

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref<'create' | 'edit' | 'view'>('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()


const openForm = async (rows?: any[] | any) => {
  visible.value = true
  if (rows) {
    formType.value = 'edit'
    dialogTitle.value = '修改专利维保'
    const data = await MaintenanceApi.getMaintenance(rows)
    formData.value = {
      ...data,
      applicationDate: formatDate(data.applicationDate, 'YYYY-MM-DD'),
      authorizationDate: formatDate(data.authorizationDate, 'YYYY-MM-DD')
    }
    //查询子表信息
    const request = {
      type: 4,
      maintenanceId: rows
    }
    const res = await SubclassApi.querySubclass(request)
    inventorData.value.agencyList = res.agencyList || []
    inventorData.value.inventorList = res.inventorList || []
    inventorData.value.applicationList = res.applicationList || []
    inventorData.value.attachmentList = res.attachmentList || []

    console.log(inventorData.value)
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加专利维保'
    resetForm()
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    originalApplicant: undefined,
    ownership: undefined,
    applicationNo: undefined,
    publicNo: undefined,
    patentName: undefined,
    protectionPoint: undefined,
    attachmentIds: [] as string[],
    models: [] as string[],
    patentType: undefined,
    applicationDate: undefined,
    authorizationDate: undefined,
    validityPeriod: undefined,
    inventor: undefined,
    actualInventor: undefined,
    acquisitionMethod: undefined,
    legalStatus: 0,
    status: 0,
    agency: undefined,
    families: undefined,
    maintenanceAssessment: undefined
  }

  // 清空 inventorData
  inventorData.value = {
    inventorList: [],
    applicationList: [],
    agencyList: [],
    attachmentList: []
  }
}
const emit = defineEmits(['success'])
const submitForm = async () => {
  const dataFromInventorInfo = inventorInfoRef.value.getData()
  const dataFromAttachmentInfo = attachmentRef.value.getData()
  console.log('从 InventorInfo 获取的数据:', dataFromInventorInfo)
  console.log('从 AttachmentInfo 获取的数据:', dataFromAttachmentInfo)
  await formRef.value.validate()
  loading.value = true
  console.log(formData.value)
  try {
    if (formType.value === 'edit') {
      await MaintenanceApi.updateMaintenance(formData.value)
      const request = {
        type: 4,
        agencyList: dataFromInventorInfo.agencyList,
        applicationList: dataFromInventorInfo.applicationList,
        inventorList: dataFromInventorInfo.inventorList,
        attachmentList: dataFromAttachmentInfo.attachmentList, //从附件组件获取
        maintenanceId: formData.value.id
      }
      await SubclassApi.batchUpdate(request)
      message.success('修改成功')
    } else {
      const maintenanceId = await MaintenanceApi.createMaintenance(formData.value)
      const request = {
        type: 4,
        agencyList: dataFromInventorInfo.agencyList,
        applicationList: dataFromInventorInfo.applicationList,
        inventorList: dataFromInventorInfo.inventorList,
        attachmentList: dataFromAttachmentInfo.attachmentList, //从附件组件获取
        maintenanceId: maintenanceId
      }
      await SubclassApi.batchCreate(request)
      message.success('创建成功')
    }
    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>

<style scoped lang="scss">
/** header tabs 样式 */
:deep(.header-tabs-container) {
  border: none;
  border-radius: 2px;
  background-color: #ebeff3;

  & > .el-tabs__header {
    background-color: #fff !important;
    border-bottom: 0.3px solid #d8d8d8;
    // border-bottom-left-radius: 20px;
    // border-bottom-right-radius: 20px;

    .el-tabs__nav {
      padding: 0 20px;
    }
    .el-tabs__item {
      height: 2rem;
      font-size: 1rem;
      color: var(--primary-text-color);
      &.is-active {
        border-top: 3px solid var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }

  & > .el-tabs__content {
    padding: 0;
  }
}
</style>
