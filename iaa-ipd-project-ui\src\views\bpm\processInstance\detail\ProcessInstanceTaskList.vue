<template>
  <el-card v-loading="loading" class="box-card">
    <template #header>
      <span class="el-icon-picture-outline">审批记录</span>
    </template>
    <el-col :offset="3" :span="18">
      <div class="block">
        <el-timeline>
          <el-timeline-item
            v-if="processInstance.endTime"
            :type="getProcessInstanceTimelineItemType(processInstance)"
            :timestamp="formatDate(processInstance?.endTime)"
            placement="top"
          >
            <el-card
              :header-style="{ padding: '5px' }"
              :body-style="{ padding: '5px' }"
              shadow="never"
            >
              <template #header>
                流程结束
                <dict-tag
                  :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS"
                  :value="processInstance.status"
                />
              </template>
              <div class="p-5px bg-#eaeef1 rounded-3px color-#565656"> 流程结束 </div>
            </el-card>
          </el-timeline-item>
          <el-timeline-item
            v-for="(item, index) in tasks"
            :key="index"
            :type="getTaskTimelineItemType(item)"
            :timestamp="formatDate(item?.endTime)"
            placement="top"
          >
            <el-card
              :header-style="{ padding: '5px' }"
              :body-style="{ padding: '5px' }"
              shadow="never"
            >
              <template #header>
                {{ item.name }}
                <dict-tag :type="DICT_TYPE.BPM_TASK_STATUS" :value="item.status" />
                <el-button
                  class="ml-10px"
                  v-if="!isEmpty(item.children)"
                  @click="openChildrenTask(item)"
                  size="small"
                >
                  <Icon icon="ep:memo" /> 子任务
                </el-button>
                <el-button
                  type="primary"
                  class="ml-10px"
                  size="small"
                  v-if="item.formId > 0"
                  @click="handleFormDetail(item)"
                  link
                >
                  <Icon icon="ep:memo" />
                  查看表单
                </el-button>
              </template>
              <div
                v-if="item.assigneeUser && item.assigneeUser.id != 1"
                class="p-5px bg-#97b2d1 rounded-3px color-#fff"
              >
                <Icon icon="ep:user" />
                {{ item.assigneeUser.nickname }}
              </div>
              <div v-if="item.reason" class="p-5px bg-#eaeef1 rounded-3px color-#3271ae m-t-5px">
                {{ item.reason }}
              </div>
              <div
                v-if="item.durationInMillis"
                class="p-5px bg-#eaeef1 rounded-3px color-#565656 m-t-5px"
              >
                耗时:{{ formatPast2(item?.durationInMillis) }}
              </div>
              <div v-else class="p-5px bg-#eaeef1 rounded-3px color-#565656 m-t-5px">
                已等待:{{ dayjs().diff(dayjs(formatDate(item?.createTime)), 'days') }}天
              </div>
              <!-- <div v-if="item.createTime" style="font-weight: normal">
                创建时间：
                <label style="font-weight: normal; color: #8a909c">
                  {{ formatDate(item?.createTime) }}
                </label>
              </div>
              <div v-if="item.endTime" style="font-weight: normal">
                审批时间：
                <label v-if="item.endTime" style="font-weight: normal; color: #8a909c">
                  {{ formatDate(item?.endTime) }}
                </label>
              </div> -->

              <!-- <div v-if="item.durationInMillis" style="font-weight: normal">
                耗时：
                <label v-if="item.durationInMillis" style="font-weight: normal; color: #8a909c">
                  {{ formatPast2(item?.durationInMillis) }}
                </label>
              </div> -->
              <!-- <p v-if="item.reason"> 审批建议：{{ item.reason }} </p> -->
            </el-card>
          </el-timeline-item>
          <el-timeline-item
            type="success"
            :timestamp="formatDate(processInstance?.startTime)"
            placement="top"
          >
            <el-card
              :header-style="{ padding: '5px' }"
              :body-style="{ padding: '5px' }"
              shadow="never"
            >
              <template #header>提交单据</template>
              <div class="p-5px bg-#eaeef1 rounded-3px color-#565656">
                <Icon icon="ep:user" />
                {{ processInstance.startUser?.nickname }}
                提交送审
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-col>
  </el-card>

  <!-- 弹窗：子任务  -->
  <TaskSignList ref="taskSignListRef" @success="refresh" />
  <!-- 弹窗：表单 -->
  <Dialog title="表单详情" v-model="taskFormVisible" width="600">
    <form-create
      ref="fApi"
      v-model="taskForm.values"
      :option="taskForm.option"
      :rule="taskForm.rule"
    />
  </Dialog>
</template>
<script lang="ts" setup>
import { formatDate, formatPast2 } from '@/utils/formatTime'
import { propTypes } from '@/utils/propTypes'
import { DICT_TYPE } from '@/utils/dict'
import { isEmpty } from '@/utils/is'
import TaskSignList from './dialog/TaskSignList.vue'
import { setConfAndFields2 } from '@/utils/formCreate'
import dayjs from 'dayjs'

defineOptions({ name: 'BpmProcessInstanceTaskList' })

defineProps({
  loading: propTypes.bool, // 是否加载中
  processInstance: propTypes.object, // 流程实例
  tasks: propTypes.arrayOf(propTypes.object) // 流程任务的数组
})

/** 获得流程实例对应的颜色 */
const getProcessInstanceTimelineItemType = (item: any): any => {
  if (item.status === 2) {
    return 'success'
  }
  if (item.status === 3) {
    return 'danger'
  }
  if (item.status === 4) {
    return 'warning'
  }
  return ''
}

/** 获得任务对应的颜色 */
const getTaskTimelineItemType = (item: any): any => {
  if ([0, 1, 6, 7].includes(item.status)) {
    return 'primary'
  }
  if (item.status === 2) {
    return 'success'
  }
  if (item.status === 3) {
    return 'danger'
  }
  if (item.status === 4) {
    return 'info'
  }
  if (item.status === 5) {
    return 'warning'
  }
  return ''
}

/** 子任务 */
const taskSignListRef = ref()
const openChildrenTask = (item: any) => {
  taskSignListRef.value.open(item)
}

/** 查看表单 */
const fApi = ref<any>() // form-create 的 API 操作类
const taskForm = ref({
  rule: [],
  option: {},
  values: {}
}) // 流程任务的表单详情
const taskFormVisible = ref(false)
const handleFormDetail = async (row) => {
  // 设置表单
  setConfAndFields2(taskForm, row.formConf, row.formFields, row.formVariables)
  // 弹窗打开
  taskFormVisible.value = true
  // 隐藏提交、重置按钮，设置禁用只读
  await nextTick()
  fApi.value?.fapi?.btn.show(false)
  fApi.value?.fapi?.resetBtn.show(false)
  fApi.value?.fapi?.disabled(true)
}

/** 刷新数据 */
const emit = defineEmits(['refresh']) // 定义 success 事件，用于操作成功后的回调
const refresh = () => {
  emit('refresh')
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 5px;
}

.tag-class {
  width: 100%;
  justify-content: flex-start;
  font-size: 14px;
}
:deep(.el-tag) {
  height: auto;
  padding: 2px;
}
:deep(.el-tag__content) {
  word-break: break-all; /* 或者使用 word-wrap: break-word; */
  white-space: pre-wrap; /* 保留换行符和空格 */
}
</style>
