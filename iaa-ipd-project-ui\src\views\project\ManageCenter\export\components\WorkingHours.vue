<template>
  <vxe-split-pane>
    <vxe-toolbar ref="toolbarRef" custom size="mini">
      <template #buttons>
        <el-input
          v-model="queryParams.basicsName"
          placeholder="请输入项目名"
          class="!w-200px mr-10px"
          size="small"
        />
        <el-button type="primary" @click="onList" size="small">查询</el-button>
      </template>
      <template #tools>
        <el-button type="primary" @click="handleExport" size="small">导出</el-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      height="86%"
      :header-cell-style="{ padding: '0', height: '34px' }"
      :cell-style="{ padding: '0', height: '34px', cursor: 'pointer' }"
      :cell-config="{ height: '34px' }"
      :row-config="{ isCurrent: true }"
      show-overflow
      align="center"
      border
      ref="tableRef"
      :data="list"
      stripe
      @cell-click="handleCellClick"
    >
      <vxe-column title="项目" field="basicsName" align="left" width="200" />
      <vxe-column title="等级" field="basicsLevel" width="80" />
      <vxe-column title="平台" field="basicsPlatform" width="80" />
      <vxe-column title="计划下单" field="planOrderDate" width="80" />
      <vxe-column title="实际下单" field="actualOrderDate" width="80" />
      <vxe-column title="立项时间" field="initiationDate" width="100" />
      <vxe-column title="TR1" field="trOne" width="100" />
      <vxe-column title="CDCP" field="cdcp" width="100" />
      <vxe-column title="TR2" field="trTwo" width="100" />
      <vxe-column title="TR3" field="trThree" width="100" />
      <vxe-column title="PDCP" field="pdcp" width="100" />
      <vxe-column title="TR4" field="trFour" width="100" />
      <vxe-column title="TR4A" field="trFourA" width="100" />
      <vxe-column title="TR5" field="trFive" width="100" />
      <vxe-column title="TR6" field="trSix" width="100" />
      <vxe-column title="adcp" field="adcp" width="100" />
    </vxe-table>
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="onList"
    />
  </vxe-split-pane>
  <vxe-split-pane width="400px" min-width="400px">
    <vxe-table
      height="100%"
      :header-cell-style="{ padding: '0', height: '34px' }"
      :cell-style="{ padding: '0', height: '34px', cursor: 'pointer' }"
      :cell-config="{ height: '34px' }"
      :row-config="{ isCurrent: true }"
      show-overflow
      align="center"
      border
      :data="workingHoursList"
      show-footer
      :footerMethod="footerMethodSum"
      empty-text="请选择左侧项目"
    >
      <vxe-column title="团队" field="role" />
      <vxe-column title="人员" field="manager" />
      <vxe-column title="实际工时" field="actualHours" />
    </vxe-table>
  </vxe-split-pane>
</template>

<script lang="ts" setup>
import { ActivitiesApi } from '@/api/project/activities'
import download from '@/utils/download'

const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  basicsName: undefined
})
const list = ref<any[]>([])
const total = ref(0)

const workingHoursList = ref<any[]>([])

const tableRef = ref()
const toolbarRef = ref()

const message = useMessage()

const onList = async () => {
  const res = await ActivitiesApi.getCruxInfoPage(queryParams.value)
  list.value = res.list
  total.value = res.total
}

const handleCellClick = async ({ row }) => {
  const res = await ActivitiesApi.getWorkingHoursTotal(row.id)
  workingHoursList.value = res
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    const data = await ActivitiesApi.exportWorkingHours(queryParams.value)
    download.excel(data, '项目工时信息.xlsx')
  } catch {
  } finally {
  }
}

/** 表尾合计函数 */
function footerMethodSum({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '合计'
      }
      if (_columnIndex === 1) return
      return sumNum(data, column.field)
    })
  ]
  return footerData
}

// 进行合计
function sumNum(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    total += costForm[i][type]
  }
  return total
}

onMounted(() => {
  onList()

  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>
