<template>
  <template v-if="formData.targetType === 3">
    <el-empty description="无需输出" />
  </template>
  <!-- 附件输出 -->
  <template v-else-if="[0, 4].includes(formData.targetType!)">
    <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
      <el-collapse-item
        title="输出参考"
        name="1"
        v-if="formData.mold == 0 && formData.targetType == 0"
      >
        <div class="flex">
          <div
            class="cursor-pointer w-70px h-120px p-5px hover:bg-#f8f8f8"
            v-for="file in fileTemplateList.filter((item) =>
              formData.targetTemplateIds?.includes(item.id)
            )"
            :key="file.id"
            @click="officeEditorRef.open(file.infraFileId, file.name)"
          >
            <img :src="getImagePath(file.uri, file.hasFolder)" class="w-60px h-60px" />
            <div
              class="line-clamp-2 overflow-hidden [display:-webkit-box] [-webkit-box-orient:vertical] [-webkit-line-clamp:2] h-40px text-.65vw word-break-normal"
            >
              {{ file.name }}
            </div>
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item title="附件输出" name="2">
        <el-button
          type="primary"
          plain
          class="!w-full mb-10px"
          size="small"
          @click="activitiesQuoteFileRef?.openForm()"
          v-if="
            allowPermission &&
            props.data.status !== 10 &&
            props.data.progress !== 100 &&
            allowTheOutput
          "
        >
          引用文件
        </el-button>
        <vxe-table
          class="w-100%"
          :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
          :cell-style="{ padding: '5px', height: '30px' }"
          show-overflow
          :data="attachmentList"
          align="center"
          border
        >
          <vxe-column title="文件名" field="name" min-width="200" align="left">
            <template #default="{ row }">
              <el-link
                type="primary"
                @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
              >
                {{ row.name }}
              </el-link>
            </template>
          </vxe-column>
          <vxe-column title="版本" field="currentVersion" width="60" />
          <vxe-column title="绑定参考" field="templateId" width="200">
            <template #default="{ row }">
              <el-select v-model="row.templateId" :disabled="true">
                <el-option
                  v-for="item in props.fileTemplateList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </vxe-column>
          <vxe-column title="审签状态" field="approvalStatus" width="90">
            <template #default="{ row }">
              <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
            </template>
          </vxe-column>
          <vxe-column
            title="审核通过时间"
            field="approvalTime"
            :formatter="dateFormatter3"
            width="120"
          />
          <vxe-column title="操作" width="100px" fixed="right" align="center">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                size="small"
                v-if="![1, 0].includes(row.approvalStatus) && row.creator === getUser.id"
                @click="fileUploadAnewRef?.openForm(row)"
              >
                重传
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                v-if="row.approvalStatus != 1 && row.creator === getUser.id"
                @click="delAttachment(row)"
              >
                删除
              </el-button>
              <!-- <el-button
                type="primary"
                link
                size="small"
                v-if="row.approvalStatus == 1"
                @click="downloadAttachment(row)"
              >
                下载
              </el-button> -->
            </template>
          </vxe-column>
        </vxe-table>
      </el-collapse-item>
    </el-collapse>
  </template>
  <template v-else>
    <el-empty description="开发中，暂不支持" />
  </template>
  <FileUploadAnew
    category="activities"
    :dynamic-id="formData.id!"
    ref="fileUploadAnewRef"
    :file-template-list="
      fileTemplateList.filter((item) => formData.targetTemplateIds?.includes(item.id))
    "
    :target-type="formData.targetType"
    :target-docking-id="formData.targetDockingId"
    @success="onListAttachment"
  />
  <OfficeEditor ref="officeEditorRef" :download="true" />
  <AttachmentPreview ref="attachmentPreviewRef" />
  <ActivitiesQuoteFile
    :basics-id="props.data?.basicsId"
    :file-template-list="props.fileTemplateList || []"
    :target-template-ids="props.data?.targetTemplateIds || []"
    :activities-id="props.data?.id"
    ref="activitiesQuoteFileRef"
    @view="attachmentPreviewRef?.openForm"
    @success="onListAttachment"
  />
</template>

<script lang="ts" setup>
import { ActivitiesVO } from '@/api/project/activities'
import { propTypes } from '@/utils/propTypes'
import { getImagePath } from '@/utils/icon'
import { dateFormatter3 } from '@/utils/formatTime'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { ElMessageBox } from 'element-plus'
import FileUploadAnew from '../../components/FileUploadAnew.vue'
import AttachmentPreview from '../../components/AttachmentPreview.vue'
import { useUserStore } from '@/store/modules/user'
import { downloadByOnlineUrl } from '@/utils/filt'
import * as FileApi from '@/api/infra/file'
import ActivitiesQuoteFile from './ActivitiesQuoteFile.vue'

const props = defineProps({
  data: propTypes.oneOf<ActivitiesVO>([]).isRequired,
  fileTemplateList: propTypes.arrayOf<any>([]).def([]),
  edit: propTypes.bool.def(false)
})

const formData = ref<ActivitiesVO>({})
const officeEditorRef = ref()
const attachmentList = ref<AttachmentRespVO[]>([])
// const router = useRouter()
const activeNames = ref(['1', '2'])
const message = useMessage()
const fileUploadAnewRef = ref()
const attachmentPreviewRef = ref()
const { getUser } = useUserStore()
const activitiesQuoteFileRef = ref()

const downloadAttachment = async (row: any) => {
  const res = await FileApi.get(row.infraFileId)
  downloadByOnlineUrl(res.url, row.name)
}
/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'activities',
    dynamicId: formData.value.id
  })
  attachmentList.value = res
}

const allowPermission = computed(() => {
  return props.data.director?.includes(getUser.id) || props.data.coordinate?.includes(getUser.id)
})

const allowTheOutput = computed(() => {
  if (!props.data.dependencies || props.data.dependencies.length == 0) {
    return true
  }
  let allow = true
  props.data.dependencies.forEach((item) => {
    if (item.dependencyType == 'fs' && item.progress != 100) {
      allow = false
    }
  })

  return allow
})

/** 跳转流程详情 */
// const toBpm = (id: string) => {
//   router.push({
//     name: 'BpmProcessInstanceDetail',
//     query: {
//       id: id
//     }
//   })
// }

const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

defineExpose({
  onListAttachment
})

watch(
  () => props.data,
  () => {
    formData.value = props.data
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__header) {
  background-color: #fafafa !important;
  border: 0.3px dashed var(--el-color-info-light-5) !important;
  border-left: 5px solid var(--el-color-primary) !important;
  font-size: 1rem;
  height: 1.8rem;
}
</style>
