<template>
  <ContentWrap>
    <div class="h-[calc(100vh-140px)]">
      <el-tabs
        type="border-card"
        class="header-tabs-container"
        v-model="activeTab"
        @tab-click="handleClick"
      >
        <el-tab-pane label="针对专利许可、转让" name="activities" style="height: 80vh">
          <vxe-toolbar size="mini" custom ref="toolbarRef">
            <template #buttons>
              <el-button type="primary" size="small" plain @click="operationFormRef?.openForm(0)">
                新增
              </el-button>
            </template>
          </vxe-toolbar>
          <!-- 表格容器 -->
          <div class="h-[calc(100%-42px)]">
            <vxe-table
              ref="tableRef"
              height="100%"
              :header-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :row-style="{
                cursor: 'pointer'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :data="list"
              border
              align="center"
            >
              <vxe-column type="checkbox" width="50" />
              <vxe-column title="原申请人" field="originalApplicant" min-width="120">
                <template #header>
                  <div>原申请人</div>
                  <el-input
                    v-model="queryParams.originalApplicant"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="当前权力人" field="ownership" min-width="120">
                <template #header>
                  <div>当前权力人</div>
                  <el-input
                    v-model="queryParams.ownership"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="申请号" field="applicationNo" min-width="120">
                <template #header>
                  <div>申请号</div>
                  <el-input
                    v-model="queryParams.applicationNo"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="专利名称" field="patentName" min-width="120">
                <template #header>
                  <div>专利名称</div>
                  <el-input
                    v-model="queryParams.patentName"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="保护技术点" field="protectionPoint" min-width="120" />
              <vxe-column title="许可人或转让人" field="licensorPeople" min-width="120" />
              <vxe-column
                title="许可或转让时间"
                field="licensorDate"
                :formatter="dateFormatter4"
                min-width="120"
              />
              <vxe-column title="费用" field="costs" min-width="120" />
              <vxe-column title="手续合格通知书" field="notice" min-width="120" />
              <vxe-column title="创建时间" min-width="150" field="createTime" :formatter="dateFormatter3" />
              <vxe-column title="操作" min-width="100" fixed="right">
                <template #default="{ row }">
                  <el-button
                    @click="operationFormRef?.openForm(queryParams.originalType, row.id)"
                    link
                    type="primary"
                    v-hasPermi="['patent:operation:update']"
                    >修改</el-button
                  >
                  <el-button
                    @click="handleDelete(row.id)"
                    link
                    type="danger"
                    v-hasPermi="['patent:operation:delete']"
                    >删除</el-button
                  >
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="无效或被无效/或诉讼" name="problem" style="height: 80vh">
          <vxe-toolbar size="mini" custom ref="toolbarRef">
            <template #buttons>
              <el-button type="primary" size="small" plain @click="operationFormRef?.openForm(1)">
                新增
              </el-button>
            </template>
          </vxe-toolbar>
          <!-- 表格容器 -->
          <div class="h-[calc(100%-42px)]">
            <vxe-table
              ref="tableRef"
              height="100%"
              :header-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :row-style="{
                cursor: 'pointer'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :data="list"
              border
              align="center"
            >
              <vxe-column type="checkbox" width="50" />
              <vxe-column title="申请人" field="applicant" min-width="120">
                <template #header>
                  <div>申请人</div>
                  <el-input
                    v-model="queryParams.applicant"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="被申请人" field="beApplicant" min-width="120">
                <template #header>
                  <div>被申请人</div>
                  <el-input
                    v-model="queryParams.beApplicant"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="涉案专利号" field="patentInvolved" min-width="120">
                <template #header>
                  <div>涉案专利号</div>
                  <el-input
                    v-model="queryParams.patentInvolved"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="涉案专利名称" field="patentInvolvedName" min-width="120">
                <template #header>
                  <div>涉案专利名称</div>
                  <el-input
                    v-model="queryParams.patentInvolvedName"
                    @change="handleList"
                    clearable
                    placeholder="按回车筛选"
                    style="width: 100%"
                    size="small"
                  />
                </template>
              </vxe-column>
              <vxe-column title="涉案专利保护点" field="patentInvolvedPoint" min-width="120" />
              <vxe-column title="涉及产品型号" field="patentModel" min-width="120" />
              <vxe-column title="诉讼案件来源" field="litigationSource" min-width="120" />
              <vxe-column title="内部侵权分析报告" field="analysisReport" min-width="120" />
              <vxe-column title="诉讼选择或可行性" field="litigationSelect" min-width="120" />
              <vxe-column title="诉讼或无效案件案号" field="litigationCase" min-width="120" />
              <vxe-column title="委托机构及律师" field="entrustedLawyer" min-width="120" />
              <vxe-column
                title="开庭时间"
                field="trialDate"
                :formatter="dateFormatter4"
                min-width="120"
              />
              <vxe-column title="诉讼或无效结论" field="litigationConclusion" min-width="120" />
              <vxe-column title="创建时间" min-width="150" field="createTime" :formatter="dateFormatter3" />
              <vxe-column title="操作" min-width="100" fixed="right">
                <template #default="{ row }">
                  <el-button
                    @click="operationFormRef?.openForm(queryParams.originalType, row.id)"
                    link
                    type="primary"
                    v-hasPermi="['patent:operation:update']"
                    >修改</el-button
                  >
                  <el-button
                    @click="handleDelete(row.id)"
                    link
                    type="danger"
                    v-hasPermi="['patent:operation:delete']"
                    >删除</el-button
                  >
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </el-tab-pane>
      </el-tabs>
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <OperationForm ref="operationFormRef" @success="getList()" />
  </ContentWrap>
</template>
<script setup lang="ts">
import { OperationApi, OperationVO } from '@/api/patent/operation'
import OperationForm from './OperationForm.vue'
import { dateFormatter4, dateFormatter3 } from '@/utils/formatTime'
import { ref, onMounted, nextTick, unref } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'

// 默认选中"针对专利许可、转让"标签
const activeTab = ref('activities')
// 消息弹窗
const message = useMessage()
const operationFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<OperationVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  originalType: 0, // 默认为0，对应"针对专利许可、转让"
  originalApplicant: undefined,
  patentName: undefined,
  ownership: undefined,
  applicationNo: undefined,
  applicant: undefined,
  beApplicant: undefined,
  patentInvolved: undefined,
  patentInvolvedName: undefined
})

// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

// 标签切换处理
const handleClick = (tab: any) => {
  if (tab.props.name === 'activities') {
    queryParams.value.originalType = 0
  } else if (tab.props.name === 'problem') {
    queryParams.value.originalType = 1
  }
  // 切换标签时清空查询条件并重新加载数据
  handleTypeChange()
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const data = await OperationApi.getOperationPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 清空查询条件并重新加载数据
const handleTypeChange = () => {
  // 清空其他查询条件
  queryParams.value.originalApplicant = undefined
  queryParams.value.patentName = undefined
  queryParams.value.ownership = undefined
  queryParams.value.applicationNo = undefined
  queryParams.value.applicant = undefined
  queryParams.value.beApplicant = undefined
  queryParams.value.patentInvolved = undefined
  queryParams.value.patentInvolvedName = undefined
  // 触发列表更新
  handleList()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await OperationApi.deleteOperation(id)
  message.success('删除成功')
  await getList()
}

onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  // 页面加载时获取默认标签的数据
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}

/** header tabs 样式 */
:deep(.header-tabs-container) {
  border: none;
  border-radius: 2px;
  background-color: #ebeff3;

  & > .el-tabs__header {
    background-color: #fff !important;
    border-bottom: 0.3px solid #d8d8d8;
    // border-bottom-left-radius: 20px;
    // border-bottom-right-radius: 20px;

    .el-tabs__nav {
      padding: 0 20px;
    }
    .el-tabs__item {
      height: 2rem;
      font-size: 1rem;
      color: var(--primary-text-color);
      &.is-active {
        border-top: 3px solid var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }

  & > .el-tabs__content {
    padding: 0;
  }
}
</style>
