<template>
  <vxe-split-pane width="300px" min-width="300px" name="risk-condition">
    <div class="p-10px">
      <el-form id="problem-condition" class="custom-form" label-width="70px">
        <el-form-item label="项目">
          <el-input v-model="formData.basicsName" />
        </el-form-item>
        <el-form-item label="项目状态">
          <el-select v-model="formData.basicsStatus" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-tree-select
            ref="treeRef"
            v-model="formData.category"
            :data="RiskCategory"
            :props="{ children: 'children', label: 'label', value: 'value' }"
            empty-text="加载中，请稍后"
            node-key="id"
            clearable
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="formData.content" />
        </el-form-item>
        <el-form-item label="预计后果">
          <el-input v-model="formData.consequence" />
        </el-form-item>
        <el-form-item label="影响">
          <el-select v-model="formData.affect" multiple>
            <el-option
              v-for="dict in getStrDictOptions('project_risk_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="概率">
          <el-select v-model="formData.probability" multiple>
            <el-option
              v-for="dict in getStrDictOptions('project_risk_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="等级">
          <el-select v-model="formData.level" multiple>
            <el-option
              v-for="dict in getStrDictOptions('project_risk_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划应对措施">
          <el-input v-model="formData.solutionsPlan" type="textarea" :rows="5" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="formData.status" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_risk_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发生时间">
          <el-date-picker
            type="daterange"
            v-model="formData.onsetDate"
            unlink-panels
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="实际应对措施">
          <el-input v-model="formData.solutionsActual" type="textarea" :rows="5" />
        </el-form-item>
        <el-form-item label="负责人">
          <UserAvatarList
            v-model="formData.director!"
            :user-list="userList"
            :size="28"
            :limit="3"
          />
        </el-form-item>
      </el-form>
    </div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div class="p-10px h-full">
      <vxe-toolbar custom size="mini" ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" @click="handleList"> 查询 </el-button>
          <el-button type="warning" size="small" @click="refresh"> 重置 </el-button>
        </template>
        <template #tools>
          <el-button type="primary" size="small" @click="handleExport"> 导出 </el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        height="86%"
        :header-cell-style="{ padding: '0', height: '34px' }"
        :cell-style="{ padding: '0', height: '34px' }"
        show-overflow
        align="center"
        border
        ref="tableRef"
        :data="riskList"
        :loading="loading"
        :export-config="{
          remote: true,
          exportMethod: handleExport
        }"
      >
        <vxe-column title="项目名称" field="basicsName" width="200" align="left" />
        <vxe-column title="项目等级" field="basicsLevel" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.basicsLevel) }}
          </template>
        </vxe-column>
        <vxe-column title="项目状态" field="basicsStatus" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_status', row.basicsStatus) }}
          </template>
        </vxe-column>
        <vxe-column title="分类" field="category" width="135" align="center">
          <template #default="{ row }">
            <el-tag>{{ getCategory(row.category) }}</el-tag>
          </template>
        </vxe-column>
        <vxe-column title="描述" field="content" width="200" />
        <vxe-column title="预计后果" field="consequence" width="200" />
        <vxe-column title="影响" field="affect" width="60" align="center">
          <template #default="{ row }">
            <DictTag type="project_risk_level" :value="row.affect" />
          </template>
        </vxe-column>
        <vxe-column title="概率" field="probability" width="60" align="center">
          <template #default="{ row }">
            <DictTag type="project_risk_level" :value="row.probability" />
          </template>
        </vxe-column>
        <vxe-column title="等级" field="level" width="80" align="center">
          <template #default="{ row }">
            <DictTag type="project_risk_level" :value="row.level" />
          </template>
        </vxe-column>
        <vxe-column title="计划应对措施" field="solutionsPlan" width="200" />
        <vxe-column title="提出人" field="creator" width="120" align="center">
          <template #default="{ row }">
            {{ getUserNickName(row.creator) }}
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="director" width="120" align="center">
          <template #default="{ row }">
            {{ getUserNickName(row.director) }}
          </template>
        </vxe-column>
        <vxe-column title="状态" field="status" width="90" align="center">
          <template #default="{ row }">
            <DictTag type="project_risk_status" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column title="发生时间" field="onsetDate" width="120" :formatter="dateFormatter3" />
        <vxe-column title="实际应对措施" field="solutionsActual" width="200" />
      </vxe-table>
      <Pagination
        :total="total"
        v-model:page="formData.pageNo"
        v-model:limit="formData.pageSize"
        @pagination="onList"
      />
    </div>
  </vxe-split-pane>
</template>

<script lang="ts" setup>
import { getDictLabel, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter3 } from '@/utils/formatTime'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import { RiskApi } from '@/api/project/risk'
import download from '@/utils/download'
import { RiskCategory } from '@/views/project/ProjectCenter/main/risk/util'

const loading = ref(false)
const riskList = ref<any[]>([])
const total = ref(0)
const userList = ref<UserVO[]>([])
const message = useMessage()
const toolbarRef = ref()
const tableRef = ref()

const formData = ref({
  basicsName: undefined,
  basicsStatus: undefined,
  category: undefined,
  content: undefined,
  consequence: undefined,
  affect: undefined,
  probability: undefined,
  level: undefined,
  solutionsPlan: undefined,
  status: undefined,
  onsetDate: undefined,
  solutionsActual: undefined,
  director: undefined,
  pageNo: 1,
  pageSize: 20
})

const handleList = () => {
  formData.value.pageNo = 1
  onList()
}

const onList = async () => {
  loading.value = true
  try {
    const res = await RiskApi.getRiskPage(formData.value)
    riskList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    basicsName: undefined,
    basicsStatus: undefined,
    category: undefined,
    content: undefined,
    consequence: undefined,
    affect: undefined,
    probability: undefined,
    level: undefined,
    solutionsPlan: undefined,
    status: undefined,
    onsetDate: undefined,
    solutionsActual: undefined,
    director: undefined,
    pageNo: 1,
    pageSize: 20
  }
  handleList()
}

const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    loading.value = true
    const data = await RiskApi.exportRiskMulti(formData.value)
    download.excel(data, '风险信息.xlsx')
  } catch {
  } finally {
    loading.value = false
  }
}

const getCategory = (category: string) => {
  const allChildren = RiskCategory.flatMap((item) => item.children)
  const found = allChildren.find((child) => child.value === category)
  return found?.label ?? ''
}

const onListUser = async () => {
  userList.value = await getSimpleUserList()
}
/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname)
    .join(',')
}

onMounted(() => {
  onListUser()
  handleList()

  nextTick(() => {
    unref(tableRef).connect(unref(toolbarRef))
  })
})
</script>
