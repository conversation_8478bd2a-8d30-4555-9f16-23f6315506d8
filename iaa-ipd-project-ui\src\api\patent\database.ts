import request from '@/config/axios'

export interface DatabaseVO {
  id: any // 主键
  word: any // 知识产权搜索词
  startDate: any // 查询开始时间
  endDate: any // 查询结束时间
  patentNo: any // 专利号
  publicNo: any // 公开号
  patentName: any // 专利名称
  patentType: any // 专利类型
  ownership: any // 权力人
  inventor: any // 发明人
  applicationDate: any // 申请日
  authorizationDate: any // 授权日
  families: any // 同族专利
  legalStatus: any // 法律状态
  abstracts: any // 摘要
  attachmentIds:any // 附件ID
  machineType: any // 保护的整机类型
  component: any // 保护的组件类型
  part: any // 保护的零件类型
  technologyPoint: any // 技术点
  solvedProblem: any // 解决的问题
  functionPoint: any // 功能点
}

export const DatabaseApi = {
  /** 分页获取知识产权数据库 */
  getDatabasePage: (data: any) => {
    return request.post({ url: '/patent/database/page', data })
  },
  /** 创建知识产权数据库 */
  createDatabase: (data: DatabaseVO) => {
    return request.post({ url: '/patent/database/create', data })
  },
  /** 更新知识产权数据库 */
  updateDatabase: (data: DatabaseVO) => {
    return request.post({ url: '/patent/database/update', data })
  },
  /** 删除知识产权数据库 */
  deleteDatabase: (id: number) => {
    return request.get({ url: `/patent/database/delete/${id}`})
  },
  /** 查询知识库详情 */
  getDatabase: async (id: any) => {
    return await request.get({ url: `/patent/database/get?id=` + id })
  },
}