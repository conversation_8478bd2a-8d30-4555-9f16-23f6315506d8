<template>
  <div class="attachment-info">
    <!-- 申请文件表格 -->
    <div class="table-section">
      <div class="table-header">
        <CardTitle title="文件" />
        <el-button size="small" v-if="!isViewMode" @click="fileUploadRef?.openDialog()">
          <img src="@/assets/opertion/upload.png" class="mr-10px w-20px h-20px" />
          上传文件
        </el-button>
      </div>
      <vxe-table :data="attachmentList" border size="small" class="custom-table" show-overflow>
        <vxe-column field="attachmentName" title="附件名称" min-width="200">
          <template #default="{ row }">
            <el-link type="primary" @click="handlePreview(row)">
              {{ row.attachmentName }}
            </el-link>
          </template>
        </vxe-column>
        <vxe-column
          field="attachmentUrl"
          title="附件地址"
          min-width="200"
        />
        <vxe-column
          field="attachmentRemark"
          title="附件备注"
          min-width="200"
        />
        <vxe-column field="createTime" title="上传时间" min-width="150" />
        <vxe-column title="操作" width="200" align="center">
          <template #default="{ row, $index }">
            <el-button type="primary" link size="small" @click="handleDownload(row)">
              下载
            </el-button>
            <el-button
              v-if="!isViewMode"
              type="danger"
              link
              size="small"
              @click="handleDeleteAttachment($index)"
            >
              删除
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <FileUpload
      ref="fileUploadRef"
      v-model:openFolder="openFolder"
      @success="setAttachmentTableData"
    />
    <!-- OfficeEditor 文件预览组件 -->
    <OfficeEditor ref="officeEditorRef" :hasDialog="true" :download="false" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OfficeEditor from '@/components/OfficeEditor/index.vue'
import { FileTemplateVO } from '@/api/project/file/template'
import FileUpload from './MainFileUpload.vue'

// Props
interface Props {
  mode?: 'view' | 'edit' | 'create'
  initialData?: {
    attachmentList?: any[]
  }
}

const fileUploadRef = ref()
const openFolder = ref<FileTemplateVO | any>({} as any)
const props = withDefaults(defineProps<Props>(), {
  mode: 'edit',
  initialData: () => ({})
})

// 是否为查看模式
const isViewMode = computed(() => props.mode === 'view')

// 附件数据
const attachmentList = ref<any[]>([])

// 初始化数据
const initializeData = () => {
  if (props.initialData && props.initialData.attachmentList) {
    attachmentList.value = props.initialData.attachmentList
  }
}

// 清空数据
const clearData = () => {
  attachmentList.value = []
}

// 监听 initialData 变化
watch(
  () => props.initialData,
  (newData) => {
    if (newData && newData.attachmentList) {
      initializeData()
    } else {
      clearData()
    }
  },
  { immediate: true, deep: true }
)

// 组件挂载时初始化数据
onMounted(() => {
  initializeData()
})

const setAttachmentTableData = (fileItemList: any[]) => {
  console.log(fileItemList)
  attachmentList.value = [...attachmentList.value, ...fileItemList]
}


// OfficeEditor 组件引用
const officeEditorRef = ref()

const handleDeleteAttachment = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个附件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    attachmentList.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 预览和下载相关方法
const handlePreview = (row: any) => {
  // 使用 OfficeEditor 组件预览文件
  if (row.infraFileId && row.attachmentName) {
    officeEditorRef.value?.open(row.infraFileId, row.attachmentName)
  } else {
    ElMessage.warning('文件信息不完整，无法预览')
  }
}

const handleDownload = (row: any) => {
  if (row.attachmentUrl) {
    const link = document.createElement('a')
    link.href = row.attachmentUrl
    link.download = row.attachmentName || ''
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } else {
    ElMessage.warning('文件地址不存在，无法下载')
  }
}

// 获取附件列表
const onListAttachment = async () => {
  // 可以在这里添加获取附件列表的逻辑
  return attachmentList.value
}

// 获取数据的方法
const getData = () => {
  return {
    attachmentList: attachmentList.value
  }
}

// 设置数据的方法
const setData = (data: any) => {
  if (data.attachmentList) {
    attachmentList.value = data.attachmentList
  }
}

// 暴露给父组件的方法
defineExpose({
  onListAttachment,
  getData,
  setData
})
</script>

<style lang="scss" scoped>
.attachment-info {
  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .table-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;

        &::before {
          content: '■';
          color: #409eff;
          margin-right: 5px;
        }
      }
    }

    .custom-table {
      :deep(.el-table__header) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
        }
      }

      :deep(.el-table__body) {
        tr:hover > td {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

.preview-container {
  .no-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #909399;

    p {
      margin: 20px 0;
      font-size: 16px;
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
    border-top: 1px solid #e4e7ed;
  }
}

:deep(.el-upload) {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 7px;
  }
}
</style>
