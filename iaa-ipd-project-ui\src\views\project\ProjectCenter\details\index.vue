<template>
  <ContentWrap>
    <div class="h-65vh position-relative overflow-auto">
      <el-alert
        type="warning"
        v-if="[3, 4, 1, 2].includes(formData.status!)"
        :description="statusDescription[formData.status!]"
        :closable="[3, 4].includes(formData.status!)"
        close-text="查看流程"
        @close="toBpm(formData.processInstanceId!)"
      />
      <div class="text-1rem bg-amber w-full rounded-1 break-all">
        {{
          modifyInfo
            ?.filter((item) => item.modifyField === 'update')
            ?.map((item) => item.modifyFieldName)
            ?.join('\n')
        }}
      </div>
      <el-tabs
        v-model="activeName"
        @tab-change="tabChange"
        class="position-sticky top-0 z-10 bg-#fff"
        v-loading="formLoading"
      >
        <el-tab-pane label="基本信息" name="info">
          <InfoForm
            ref="infoFormRef"
            v-model:activeName="activeName"
            :user-list="userList"
            :category-list="categoryList"
            :prop-form-data="formData"
            :formType="formType"
            v-model:edit="tempEdit"
            @update:active-name="(name) => (activeName = name)"
          />
        </el-tab-pane>
        <el-tab-pane label="团队信息" name="team">
          <TeamForm
            ref="teamFormRef"
            :user-list="userList"
            :prop-form-data="formData"
            v-model:edit="tempEdit"
          />
        </el-tab-pane>
        <el-tab-pane label="审批流" name="approval">
          <ApprovalForm
            ref="approvalFormRef"
            v-model:edit="tempEdit"
            :prop-form-data="formData"
            @update:active-name="(name) => (activeName = name)"
          />
        </el-tab-pane>
        <el-tab-pane label="权限" name="permission" v-if="formData.id">
          <PermissionForm
            ref="permissionFormRef"
            :user-list="userList"
            :basics-id="props.baseId"
            v-model:edit="tempEdit"
            :prop-form-data="formData"
          />
        </el-tab-pane>
        <el-tab-pane label="日志" name="log" v-if="formData.id">
          <LogForm
            ref="logFormRef"
            category="project"
            v-model:baseId="formData.id"
            :user-list="userList"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="modifyInfo?.length > 0" class="h-20vh overflow-auto">
      <vxe-table
        :data="modifyInfo.filter((item) => item.modifyField !== 'update')"
        show-overflow
        :header-cell-style="{ padding: 0, fontSize: '1rem' }"
        :cell-style="{ padding: '5px', fontSize: '1rem', height: '1.5rem' }"
        border
        stripe
        align="center"
      >
        <vxe-column title="修改字段" field="modifyFieldName" />
        <!-- 统一处理列的渲染逻辑 -->
        <template v-for="col in ['beforeValue', 'afterValue']" :key="col">
          <vxe-column :title="col === 'beforeValue' ? '修改前' : '修改后'">
            <template #default="{ row }">
              <template v-if="shouldShowDictTag(row.modifyField)">
                <el-tag>
                  {{ formatDictLabel(row.modifyField, row[col]) }}
                </el-tag>
              </template>
              <template
                v-else-if="
                  row.modifyField === 'managers' ||
                  getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                    (dict) => dict.value === row.modifyField
                  )?.label
                "
              >
                {{ getUserNickName(userList, row[col]) }}
              </template>
              <!-- <template v-else-if="row.modifyField === 'categoryIds'">
                {{ getCategoryName(row[col]) }}
              </template> -->
              <template v-else>{{ row[col] }}</template>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </div>
    <div class="h-40px position-relative" v-if="!id && props.formType === 'create'">
      <el-button
        type="primary"
        class="position-absolute bottom-0 end-0"
        :loading="formLoading"
        @click="submitForm"
      >
        保存
      </el-button>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import InfoForm from './components/InfoForm.vue'
import TeamForm from './components/TeamForm.vue'
import LogForm from './components/LogForm.vue'
import ApprovalForm from './components/ApprovalForm.vue'
import PermissionForm from './components/PermissionForm.vue'
import { getSimpleUserList, UserVO } from '@/api/system/user/index'
import { CategoryApi, CategoryVO } from '@/api/project/category'
import { propTypes } from '@/utils/propTypes'
import { BasicsApi, BasicsVO } from '@/api/project/basics'
import { cloneDeep } from 'lodash-es'
import { BasicsFlowVO, BasicsModifyInfoVO, BasicsFlowApi } from '@/api/bpm/basics'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { shouldShowDictTag, getUserNickName, formatDictLabel } from './components/utils'
import { ApprovalTemplate } from './components/ApprovalTemplate'

const userList = ref<UserVO[]>([]) //用户列表
const categoryList = ref<CategoryVO[]>([]) // 分类列表的数据
const activeName = ref('info') //激活的窗口
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const infoFormRef = ref()
const teamFormRef = ref()
const logFormRef = ref()
const approvalFormRef = ref()
const permissionFormRef = ref()
const tempEdit = ref(true) //修改状态参数

const { query } = useRoute() // 查询参数
const router = useRouter()

const formData = ref<BasicsVO>({
  id: undefined, //项目id
  number: undefined, // 项目编号
  name: undefined, // 项目名称
  sort: undefined, // 显示顺序
  level: undefined, // 项目等级
  managers: undefined, // 项目经理ids
  targetMarket: undefined, // 针对市场
  targetCustomer: undefined, // 针对客户
  mold: undefined, // 项目类型(0:自主研发，1:客户定制)
  releaseDate: undefined, // 产品预计发布时间
  remark: '', // 备注
  status: undefined, // 项目状态（0:进行中，1:暂停，2:完结,3:审签，4：创建）
  progress: undefined, // 项目进度
  categoryIds: [], //项目分组ids
  teams: []
})

const statusDescription = {
  1: '项目已暂停',
  2: '项目已完结',
  3: '项目审签中',
  4: '项目创建流程审批中'
}

const props = defineProps({
  id: propTypes.number.isRequired,
  baseId: propTypes.number.def(undefined),
  formType: propTypes.string.def('create'),
  edit: propTypes.bool.def(true)
})

const message = useMessage() // 消息弹窗

const modifyInfo = ref<BasicsModifyInfoVO[]>([]) // 修改记录

/** 获取用户列表 */
const listUser = async () => {
  userList.value = await getSimpleUserList()
}
/** 获取分类列表 */
const getCategoryList = async () => {
  categoryList.value = await CategoryApi.getCategoryList({})
}

/** 查询项目基础信息 */
const initProjectBase = async () => {
  const res = await BasicsApi.getBasics(props.baseId)
  formData.value = res
}

/** 跳转流程详情 */
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}
const emits = defineEmits(['create:success', 'update:success'])
/** 提交按钮 */
const submitForm = async () => {
  formLoading.value = true
  try {
    await unref(infoFormRef).validate()
    await unref(teamFormRef).validate()
    await unref(approvalFormRef).validate()
    if (!formData.value.approve) {
      let approveData = [] as any[]
      for (let key in ApprovalTemplate) {
        approveData.push({
          type: key,
          hasCrux: ApprovalTemplate[key].hasCrux,
          effectiveness: ApprovalTemplate[key].effectiveness,
          processKey: ApprovalTemplate[key].value,
          node: ApprovalTemplate[key].node
        })
      }

      formData.value.approve = approveData
    }
    formData.value.status = 4
    const deepCopyFormData = cloneDeep(unref(formData.value))
    // 确保团队成员中的项目经理（pm）字段与表单的 managers 字段同步
    if (deepCopyFormData.teams?.some((item) => item.role === 'pm')) {
      // 如果存在 pm 角色成员，更新其 userIds
      const pmTeam = deepCopyFormData.teams.find((item) => item.role === 'pm')
      if (pmTeam) {
        pmTeam.userIds = deepCopyFormData.managers || []
      }
    } else {
      // 如果不存在 pm 角色成员，创建一个新的团队成员对象
      deepCopyFormData.teams!.push({
        role: 'pm',
        userIds: deepCopyFormData.managers || []
      })
    }
    const data = {
      basicsId: deepCopyFormData.id,
      number: deepCopyFormData.number,
      name: deepCopyFormData.name,
      sort: deepCopyFormData.sort,
      platform: deepCopyFormData.platform,
      mold: deepCopyFormData.mold,
      level: deepCopyFormData.level,
      points: deepCopyFormData.points,
      managers: deepCopyFormData.managers,
      targetMarket: deepCopyFormData.targetMarket,
      targetCustomer: deepCopyFormData.targetCustomer,
      releaseDate: deepCopyFormData.releaseDate,
      planOrderDate: deepCopyFormData.planOrderDate,
      actualOrderDate: deepCopyFormData.actualOrderDate,
      status: deepCopyFormData.status,
      progress: deepCopyFormData.progress || 0,
      categoryIds: deepCopyFormData.categoryIds,
      remark: deepCopyFormData.remark,
      teams: deepCopyFormData.teams,
      approve: deepCopyFormData.approve,
      modifyInfo: []
    } as BasicsFlowVO
    if (!data.modifyInfo) {
      data.modifyInfo = []
    }
    data.modifyInfo.push({ modifyFieldName: '项目创建', modifyField: 'update' })
    await BasicsFlowApi.createBasicsFlow(data)
    message.success('项目创建流程启动成功')
    emits('create:success')
  } finally {
    formLoading.value = false
  }
}

/** 表单切换至团队信息时，刷新参数 */
const tabChange = async () => {
  await unref(teamFormRef)?.validate()
  if (activeName.value === 'log') {
    await unref(logFormRef)?.refreshLog()
  }
}
/** 更新为修改表单状态 */
const updateEditStatus = (status: boolean) => {
  tempEdit.value = status
  unref(teamFormRef)?.refreshTeam()
}

const saveEditData = async () => {
  formLoading.value = true
  try {
    const infoForm = unref(infoFormRef)
    const teamForm = unref(teamFormRef)
    const approvalForm = unref(approvalFormRef)
    const permissionForm = unref(permissionFormRef)
    await infoForm.validate()
    await teamForm.validate()
    await approvalForm.validate()
    //获取表单更新的数据描述
    let changeMsg = [] as BasicsModifyInfoVO[]
    changeMsg.push(...infoForm?.getChangeMsg())
    changeMsg.push(...teamForm?.getChangeMsg())
    changeMsg.push(...approvalForm.getChangeMsg())
    changeMsg.push(...permissionForm.getChangeMsg())

    let passChangeMsg = [] as BasicsModifyInfoVO[]

    let permission = permissionForm.getPermission()
    const deepCopyFormData = cloneDeep(unref(formData.value)) as BasicsVO
    const data = {
      basicsId: deepCopyFormData.id,
      number: deepCopyFormData.number,
      name: deepCopyFormData.name,
      sort: deepCopyFormData.sort,
      platform: deepCopyFormData.platform,
      mold: deepCopyFormData.mold,
      level: deepCopyFormData.level,
      points: deepCopyFormData.points,
      managers: deepCopyFormData.managers,
      targetMarket: deepCopyFormData.targetMarket,
      targetCustomer: deepCopyFormData.targetCustomer,
      releaseDate: deepCopyFormData.releaseDate,
      planOrderDate: deepCopyFormData.planOrderDate,
      actualOrderDate: deepCopyFormData.actualOrderDate,
      status: deepCopyFormData.status,
      progress: deepCopyFormData.progress,
      categoryIds: deepCopyFormData.categoryIds,
      remark: deepCopyFormData.remark,
      teams: deepCopyFormData.teams,
      approve: deepCopyFormData.approve,
      modifyInfo: changeMsg,
      passChangeMsg: passChangeMsg,
      permission: permission
    } as BasicsFlowVO
    if (changeMsg.length == 0) {
      await BasicsApi.updateBasics({
        ...deepCopyFormData,
        log: passChangeMsg,
        permission: permission
      })
      message.success('项目更新成功')
      emits('update:success')
    } else {
      // 二次确认
      const { value } = await ElMessageBox.prompt('请输入变更原因', '活动', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
        inputErrorMessage: '修改原因不能为空'
      })
      data.modifyInfo.push({
        modifyField: 'update',
        modifyFieldName: '项目修改原因：' + value,
        beforeValue: ''
      })
      let businessId = await BasicsFlowApi.updateBasicsFlow(data)
      message.success('项目更新流程启动成功，等待流程审签通过,进度请前往待办事项查看')
      emits('create:success', businessId)
    }
  } finally {
    formLoading.value = false
  }
}
/** 初始化流程数据 */
const initBpmBasicsData = async () => {
  const res = await BasicsFlowApi.getBasicsFlowByProcessInstanceId(query.id as string)
  formData.value = res
  modifyInfo.value = res.modifyInfo
}

// const getCategoryName = (ids: number[]) => {
//   return categoryList.value
//     .filter((item) => ids.includes(item.id))
//     .map((item) => item.name)
//     .join(',')
// }

watch(
  () => props.edit,
  () => {
    tempEdit.value = props.edit
  },
  { immediate: true }
)

defineExpose({ updateEditStatus, saveEditData })

onMounted(() => {
  if (props.formType === 'update') {
    tempEdit.value = false
    initProjectBase()
  }

  if (query.id) {
    initBpmBasicsData()
    tempEdit.value = false
  }
  listUser()
  getCategoryList()
})
</script>
