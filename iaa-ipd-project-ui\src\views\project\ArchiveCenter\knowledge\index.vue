<template>
  <el-row
    :gutter="5"
    :class="[
      props.basicsId && 'h-98%',
      !props.basicsId && 'h-[calc(100vh-90px)]',
      'p-t-10px',
      'p-b-10px'
    ]"
  >
    <el-col :span="4" :xs="24" class="h-full">
      <FileTree :category-list="categoryList" @node-click="onOpenSpecifiedLevelFolder" />
    </el-col>
    <el-col :span="20" :xs="24"  class="h-full">
      <el-card shadow="never" class="h-100%">
        <template #header>
          <div class="flex justify-between content-center">
            <div class="flex flex-wrap content-center items-center folder-path">
              <XButton pre-icon="ep:arrow-left" @click="onBackedFolder" />
              <el-breadcrumb>
                <el-breadcrumb-item @click="onOpenSpecifiedLevelFolder('0')">
                  输出物
                </el-breadcrumb-item>
                <el-breadcrumb-item
                  v-for="(path, pathIdx) in checkedPath"
                  :key="pathIdx"
                  @click="onOpenSpecifiedLevelFolder(path.value)"
                >
                  {{ path.label }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="w-260px">
              <el-input class="!w-200px" placeholder="请输入文件名称" v-model="queryParams.name" />
              <XButton type="primary" plain pre-icon="ep:search" @click="onSearch" />
            </div>
          </div>
        </template>
        <div class="top-0 bottom-40px right-0 left-0 position-absolute">
          <div class="h-40px leading-40px opertion flex">
            <div style="width: calc(100% - 120px)">
              <el-button
                size="small"
                v-if="activeFile && !activeFile?.hasFolder"
                @click="shareFile"
              >
                <img src="@/assets/opertion/share.png" class="mr-10px w-20px h-20px" />
                分享
              </el-button>
              <el-button
                size="small"
                v-if="activeFile && !activeFile?.hasFolder && activeFile.download"
                @click="handleDownloadFile"
              >
                <img src="@/assets/opertion/download.png" class="mr-10px w-20px h-20px" />
                下载
              </el-button>
              <el-button
                size="small"
                v-if="activeFile && !activeFile?.hasFolder && !activeFile.download"
                @click="openFormDownloadForm"
              >
                <img src="@/assets/opertion/download.png" class="mr-10px w-20px h-20px" />
                申请下载权限
              </el-button>
            </div>
            <div style="width: 120px">
              <el-popover class="box-item" placement="bottom-start" width="600px" trigger="click">
                <template #reference>
                  <el-badge
                    :hidden="fileDownloadFlowCount == 0"
                    :value="fileDownloadFlowCount"
                    class="item"
                  >
                    <el-button style="width: 120px" size="small">下载权限申请列表</el-button>
                  </el-badge>
                </template>
                <vxe-table
                  :header-cell-style="{ padding: 0 }"
                  :cell-style="{ padding: 0 }"
                  :data="AttachmentDownloadList"
                  height="300px"
                  border
                >
                  <vxe-column title="项目名称" field="basicsName">
                    <template #default="{ row }">
                      <el-link type="primary" @click="toBpm(row.processInstanceId)">
                        {{ row.basicsName }}
                      </el-link>
                    </template>
                  </vxe-column>
                  <vxe-column title="项目等级" field="basicsLevel" />
                  <vxe-column title="项目类型" field="basicsMold" />
                  <vxe-column title="项目平台" field="basicsPlatform" />
                  <vxe-column title="状态" field="status" align="center" width="100">
                    <template #default="{ row }">
                      <DictTag :type="DICT_TYPE.BPM_TASK_STATUS" :value="row.status" />
                    </template>
                  </vxe-column>
                </vxe-table>
              </el-popover>
            </div>
          </div>
          <div class="flex h-full">
            <draggable
              v-loading="loading"
              v-contextmenu="{ name: 'context-menu-1', id: '456' }"
              @click="openFolder && (activeFile = openFolder)"
              @contextmenu="activeFile = openFolder"
              :list="currentLevelFileList"
              class="file-list"
              :delay="10"
              handle=".draggable"
              forceFallback
              scroll
              :sort="false"
              @end="handleFileMove"
              item-key="url"
            >
              <template #item="{ element }">
                <div
                  :class="{
                    'file-item': true,
                    'is-active':
                      activeFile?.id == element.id && activeFile?.hasFolder == element.hasFolder,
                    draggable: !element.hasFolder
                  }"
                  @click.stop="handleSelected(element)"
                  @contextmenu.stop="handleSelected(element)"
                  @dblclick.stop="openFileOrFolder(element)"
                  v-contextmenu="{ name: 'context-menu-1', id: '123' }"
                  :key="`${element.hasFolder}${element.id}`"
                >
                  <img :src="getImagePath(element.url, element.hasFolder)" class="w-77px h-77px" />
                  <div class="file-name">
                    {{ element.name }}
                  </div>
                </div>
              </template>
            </draggable>
            <div class="w-300px file-info">
              <FileAttribute
                direction="bottom"
                :file="activeFile"
                :versions="templateVersions"
                :update="false"
                @open="openFileOrFolder"
              />
            </div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
  <context-menu name="context-menu-1">
    <context-menu-item :disabled="!activeFile" divider @click="openFileOrFolder(activeFile!)">
      <Icon icon="ep:arrow-right" />
      打开
    </context-menu-item>
    <context-menu-item
      :disabled="!activeFile"
      @click="handleDownloadFile"
      v-if="activeFile?.download"
    >
      <img src="@/assets/opertion/download.png" class="w-20px h-20px" />
      下载
    </context-menu-item>
    <context-menu-item
      :disabled="!activeFile"
      @click="handleDownloadFile"
      v-if="!activeFile?.download"
    >
      <img
        src="@/assets/opertion/download.png"
        class="w-20px h-20px"
        @click="openFormDownloadForm"
      />
      申请下载权限
    </context-menu-item>
    <context-menu-item :disabled="!activeFile || activeFile?.hasFolder" @click="shareFile">
      <img src="@/assets/opertion/share.png" class="w-20px h-20px" />
      分享
    </context-menu-item>
    <context-menu-item :disabled="!activeFile" @click="attributeDialog.visible = true">
      <Icon icon="ep:info-filled" />
      属性
    </context-menu-item>
  </context-menu>
  <Dialog title="文件属性" v-model="attributeDialog.visible">
    <FileAttribute direction="bottom" :file="activeFile" :update="false" />
  </Dialog>
  <OfficeEditor ref="officeEditorRef" />
  <Dialog title="文件分享(请复制链接后转发)" v-model="fileShareDialog.visible">
    <div id="fileShareUrl"></div>
    <template #footer>
      <el-button type="primary" @click="copyInnerHTML">一键复制</el-button>
    </template>
  </Dialog>
  <Dialog title="项目文件下载权限申请" v-model="downloadVisiable">
    <el-form label-width="100px">
      <el-form-item label="项目名称">
        <el-input v-model="formDownload.basicsName" :disabled="true" />
      </el-form-item>
      <el-form-item label="原因">
        <el-input v-model="formDownload.reason" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="onSubmitFormDownload">提交</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import FileTree from '@/views/project/ArchiveCenter/file-template/components/FileTree.vue'
import FileAttribute from '@/views/project/ArchiveCenter/file-template/components/FileAttribute.vue'
import { handleTree, findPath } from '@/utils/tree'
import { KnowledgeApi, KnowledgeVO } from '@/api/project/knowledge'
import draggable from 'vuedraggable'
import { getImagePath } from '@/utils/icon'
import { cloneDeep } from 'lodash-es'
import { propTypes } from '@/utils/propTypes'
import { downloadFile } from '@/api/infra/file'
import { AttachmentDownloadFlowApi } from '@/api/bpm/attachment-download'
import { DICT_TYPE } from '@/utils/dict'
import { BasicsApi } from '@/api/project/basics'

interface Path {
  label: string
  value: string
}

const props = defineProps({
  basicsId: propTypes.number.def(undefined)
})

const fileShareDialog = ref({
  visible: false
})
const attributeDialog = ref({
  visible: false
})

const categoryList = ref<KnowledgeVO[]>([])
const checkedPath = ref<Path[]>([])
const loading = ref(false)
const queryParams = ref({
  basicsId: undefined as number | undefined,
  currentId: '',
  parentId: '',
  name: ''
})
const activeFile = ref<KnowledgeVO | any>(undefined as any)
const openFolder = ref<KnowledgeVO>()
const currentLevelFileList = ref<KnowledgeVO[]>([])
const templateVersions = ref<KnowledgeVO[]>()
const officeEditorRef = ref()
const message = useMessage()
const AttachmentDownloadList = ref<any[]>([])
const router = useRouter()

const formDownload = ref({
  basicsId: undefined as number | undefined,
  basicsName: '',
  reason: ''
})
const downloadVisiable = ref(false)

const fileDownloadFlowCount = computed(() => {
  return AttachmentDownloadList.value?.filter((item) => item.status == 1).length
})

const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

const openFormDownloadForm = async () => {
  if (!activeFile.value) return
  const exists = AttachmentDownloadList.value.some(
    (item) => item.basicsId == activeFile.value.basicsId
  )
  if (exists) {
    message.error(
      '当前项目已申请过下载权限，请查看流程是否通过，如已通过，请刷新后重试或联系管理员'
    )
    return
  }
  const project = await BasicsApi.getSimpleBasics(activeFile.value.basicsId)
  downloadVisiable.value = true
  formDownload.value = {
    basicsId: project.id,
    basicsName: project.name,
    reason: ''
  }
}

const onSubmitFormDownload = async () => {
  if (!formDownload.value.reason) {
    message.error('请输入申请原因')
    return
  }
  await AttachmentDownloadFlowApi.createAttachmentDownloadFlow(formDownload.value)
  message.success('申请成功，请耐心等待审批')
  onListFileDownload()
  formDownload.value = {
    basicsId: undefined,
    basicsName: '',
    reason: ''
  }
  downloadVisiable.value = false
}

/** 打开文件夹或者打开文件 */
const openFileOrFolder = async (row: KnowledgeVO) => {
  if (!row) return
  if (row.hasFolder) {
    onOpenFolder(row)
  } else {
    officeEditorRef.value.open(row.infraFileId, row.name)
  }
}

/** 打开文件夹 */
const onOpenFolder = async (row: KnowledgeVO) => {
  queryParams.value.currentId = row.id!
  queryParams.value.basicsId = row.basicsId!
  queryParams.value.parentId = row.parentId!
  checkedPath.value.push({ label: row.name!, value: row.id! })
  openFolder.value = row
  getCurrentLevelFileList()
}
/** 选中文件 */
const handleSelected = async (row: KnowledgeVO) => {
  activeFile.value = row
  if (row.hasFolder) return
  templateVersions.value = await KnowledgeApi.getFileAllVersion(row.id!)
}

const handleFileMove = (event: any) => {
  // console.log('结束dragEnd：', event)
  // const { oldIndex, newIndex } = event
  // console.log('拖拽开始时的 oldIndex:', oldIndex, 'newIndex:', newIndex)
}

/** 下载模板 */
const handleDownloadFile = async () => {
  if (!activeFile.value) return
  try {
    await downloadFile(activeFile.value.url, activeFile.value.name)
  } catch (error) {
    message.alertError('下载文件失败')
  }
}
/** 获取当前层级的文件列表 */
const getCurrentLevelFileList = async () => {
  if (checkedPath.value.length === 0) {
    currentLevelFileList.value = cloneDeep(categoryList.value)
  } else {
    const res = await KnowledgeApi.getKnowledgeList(queryParams.value)
    currentLevelFileList.value = res
  }
}

const onSearch = async () => {
  if (queryParams.value.name) {
    checkedPath.value = []
    openFolder.value = undefined
    queryParams.value.basicsId = undefined
    queryParams.value.currentId = undefined as unknown as string
    queryParams.value.parentId = undefined as unknown as string
    const res = await KnowledgeApi.getKnowledgeList(queryParams.value)
    currentLevelFileList.value = res
  } else {
    getCurrentLevelFileList()
  }
}
/** 返回上一级文件夹 */
const onBackedFolder = () => {
  if (checkedPath.value && checkedPath.value.length > 1) {
    onOpenSpecifiedLevelFolder(checkedPath.value[checkedPath.value.length - 2].value)
  } else if (checkedPath.value && checkedPath.value.length > 0) {
    onOpenSpecifiedLevelFolder('0')
  }
}

const copyInnerHTML = () => {
  const element = document.getElementById('fileShareUrl')
  if (!element) return
  const content = element.textContent || element.innerHTML // 优先读取文本内容
  if (content) {
    navigator.clipboard
      .writeText(content)
      .then(() => {
        message.success('访问地址已复制到剪贴板')
      })
      .catch(() => {
        message.error('复制失败，请手动复制')
      })
  }
}

const shareFile = async () => {
  if (!activeFile.value) return
  fileShareDialog.value.visible = true
  await nextTick()
  const url = `${window.location.origin}/archive-center/Knowledge?type=view&id=${activeFile.value.id}`
  // 使用 textContent 避免 HTML 转义
  const element = document.getElementById('fileShareUrl')!
  element.textContent = url // 直接设置文本内容
  element.style.wordBreak = 'break-all' // 保持换行效果（可选）
}
/** 打开指定层级的文件架 */
const onOpenSpecifiedLevelFolder = async (categoryId: string) => {
  if (categoryId === '0') {
    checkedPath.value = []
    openFolder.value = undefined
  } else {
    checkedPath.value = []
    const tempPath = findPath(categoryList.value, (node: any) => node.id === categoryId)
    tempPath?.forEach((item) => {
      checkedPath.value.push({ label: item.name, value: item.id })
    })
    openFolder.value = tempPath?.[tempPath.length - 1]
  }
  queryParams.value.currentId = categoryId
  queryParams.value.basicsId = openFolder.value?.basicsId || props.basicsId
  queryParams.value.parentId = openFolder.value?.parentId as string
  await getCurrentLevelFileList()
}

/** 初始化文件分类 */
const initCategory = async () => {
  queryParams.value.basicsId = props.basicsId
  const res = await KnowledgeApi.getKnowledgeCategoryList(queryParams.value)
  categoryList.value = handleTree(res)
}

const onListFileDownload = async () => {
  const res = await AttachmentDownloadFlowApi.listAttachmentDownloadFlow()
  AttachmentDownloadList.value = res
}

watch(
  () => props.basicsId,
  async () => {
    if (props.basicsId) {
      await initCategory()
      await getCurrentLevelFileList()
    }
  },
  { immediate: true }
)

onMounted(() => {
  setTimeout(async () => {
    if (!props.basicsId) {
      await initCategory()
      await getCurrentLevelFileList()
    }
  }, 500)
  onListFileDownload()
})
</script>

<style lang="scss" scoped>
.body-container {
  height: 100%;
  overflow: auto;
}

:deep(.el-card__body) {
  padding: 0px;
  position: relative;
  height: calc(100% - 71px);
}

.file-info,
.file-list {
  height: 98% !important;
}

.file-info {
  background-color: #fdfdfd;
}

.file-list {
  padding: 20px;
  padding-bottom: 3px;
  width: calc(100% - 300px);
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: hidden;
  align-content: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.file-item {
  width: 81px;
  height: 146px;
  text-align: center;
  margin-left: 5px;

  .file-name {
    height: 69px;
    overflow: hidden;
    font-size: 12px;
    color: #999999;
    word-break: break-all; /* 关键属性：强制在单词内换行 */
    white-space: normal; /* 确保多行显示 */
  }
}

.file-item:hover,
.file-item.is-active {
  background-color: var(--el-color-primary-light-9);
  border-radius: 2px;
}

.opertion {
  border-bottom: 1px #f1f1f1 solid;
  padding: 0 20px;
}

.folder-path {
  width: 600px;
  border: 0.3px solid #f1f1f1;
  background-color: #fdfdfd;
  border-radius: 3px;
}

:deep(.el-breadcrumb__item) {
  cursor: pointer;
}
</style>
