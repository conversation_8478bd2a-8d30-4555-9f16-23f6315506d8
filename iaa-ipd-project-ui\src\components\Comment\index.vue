<!--评论模块-->
<template>
  <div class="!w-100% p-5px" v-loading="loading">
    <el-pagination
      v-model:current-page="queryParams.pageNo"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[5, 10, 20]"
      :pager-count="5"
      :total="total"
      @current-change="getList()"
      small
      background
      layout="total,prev, pager, next"
      v-if="queryParams.moduleId && total > 0"
    />
    <TransitionGroup
      name="list"
      tag="div"
      enter-active-class="animate__animated animate__fadeIn"
      leave-active-class="animate__animated animate__fadeOut"
    >
      <div
        class="!w-100% m-t-15px rounded-5px"
        v-for="item in commentsList"
        :key="item.id"
        :style="{ 'background-color': props.bgColor }"
      >
        <div class="flex">
          <img
            class="avatar border-1px border-solid rounded-50% border-color-#f1f1f1"
            :src="item.avatar || Avatar"
            style="object-fit: contain"
            width="30"
            height="30"
          />
          <div class="p-l-5px text-1rem text-#515767">{{ item.nickname }}</div>
          <div class="p-l-5px text-.8rem">{{ formatToDate(item.createTime) }}</div>
        </div>
        <div class="p-l-35px">
          <UploadImgs
            v-model="item.imgUrls"
            height="50px"
            width="50px"
            v-if="item.imgUrls && item.imgUrls.length > 0"
            :disabled="true"
            class="imgs"
          />
          <div class="text-1rem text-#474747">
            <em class="!not-italic" v-html="item.content"></em>
          </div>
          <div class="text-#8a919f text-.8rem" v-if="props.disabled">
            <span class="cursor-pointer" @click="replyComment(item, false)">
              <Icon icon="ep:chat-dot-round" class="iconfont icon-comment" />
              <span>评论</span>
            </span>
            &nbsp;&nbsp;
            <span class="cursor-pointer" @click="delCommentEvent(item)" v-if="item.hasDeleted">
              <Icon icon="ep:delete" class="iconfont icon-comment" />
              <span>删除</span>
            </span>
            &nbsp;&nbsp;
            <span
              class="cursor-pointer"
              v-if="commentReplayId && comment.parentId == commentReplayId"
              @click="
                () => {
                  comment.parentId = undefined
                  commentReplayId = undefined
                }
              "
            >
              <Icon icon="ep:close" class="iconfont icon-comment" />
              <span>取消</span>
            </span>
          </div>
        </div>
        <div class="child m-l-35px backdrop-blur bg-#ebeff3/30 rounded-5px">
          <div v-for="child in item.children" :key="child.id" class="p-r-10px m-t-10px">
            <div class="flex justify-between">
              <div class="flex p-2px items-start">
                <img
                  class="avatar border-1px border-solid rounded-50% border-color-#f1f1f1"
                  :src="item.avatar || Avatar"
                  style="object-fit: contain"
                  width="20"
                  height="20"
                />
                <div class="m-l-5px text-1rem text-#515767">{{ item.nickname }} :</div>
              </div>
              <div style="width: calc(100% - 70px)">
                <UploadImgs
                  v-model="child.imgUrls"
                  height="50px"
                  width="50px"
                  v-if="child.imgUrls && child.imgUrls.length > 0"
                  :disabled="true"
                  class="imgs"
                />
                <div
                  v-if="child.replayNickName"
                  class="text-[var(--el-color-primary)] leading-28px float-left text-1rem"
                >
                  回复{{ child.replayNickName }}&nbsp;
                </div>
                <div
                  class="text-1rem text-#474747 !not-italic !min-h-28px leading-28px"
                  v-html="child.content"
                ></div>
              </div>
            </div>
            <div class="text-#8a919f text-.6rem flex m-l-20px">
              <div class="p-l-5px text-.7rem">{{ formatToDate(item.createTime) }}</div>
              &nbsp;&nbsp;
              <div class="cursor-pointer" @click="replyComment(child, true)" v-if="props.disabled">
                <Icon icon="ep:chat-dot-round" class="iconfont icon-comment" />
                <span>回复</span>
              </div>
              &nbsp;&nbsp;
              <div class="cursor-pointer" @click="delCommentEvent(child)" v-if="child.hasDeleted&&props.disabled">
                <Icon icon="ep:delete" class="iconfont icon-comment" />
                <span>删除</span>
              </div>
              &nbsp;&nbsp;
              <div
                class="cursor-pointer"
                v-if="commentReplayId && comment.replyCommentId == commentReplayId"
                @click="
                  () => {
                    commentReplayId = undefined
                    comment.replyCommentId = undefined
                  }
                "
              >
                <Icon icon="ep:close" class="iconfont icon-comment" />
                <span>取消</span>
              </div>
            </div>
          </div>
          <CommentInput
            v-model="commentChildData"
            v-if="
              commentReplayId &&
              (comment.replyCommentId == commentReplayId || comment.parentId == commentReplayId)
            "
            @send="commitComment(true)"
          />
        </div>
      </div>
    </TransitionGroup>
    <transition
      name="fade"
      style="margin-bottom: 10px"
      v-if="queryParams.moduleId && props.allow"
      class="!position-absolute bottom-0 !w-95%"
    >
      <CommentInput
        v-model="commentData"
        :at="props.at"
        :sharp="props.sharp"
        @send="commitComment(false)"
      />
    </transition>
  </div>
</template>

<script lang="ts" name="comment" setup>
import Icon from '../Icon/src/Icon.vue'
import { CommentSaveReqVO, CommentRespVO, CommentApi } from '@/api/infra/comment'
import { formatToDate } from '@/utils/dateUtil'
import { propTypes } from '@/utils/propTypes'
import { CommentVO } from '@/components/CommentInput/comment'
import Avatar from '@/assets/imgs/logo.png'

const message = useMessage()
const loading = ref(false)
const commentsList = ref<CommentRespVO[]>([])
const total = ref(0)

const commentReplayId = ref<number | undefined>()

const commentData = ref<CommentVO>({
  content: '',
  imgs: ''
})

const commentChildData = ref<CommentVO>({
  content: '',
  imgs: ''
})

const comment = ref<CommentSaveReqVO>({})

const queryParams = ref({
  pageNo: 1,
  pageSize: 3,
  moduleId: ''
})

const props = defineProps({
  category: propTypes.string.def(''),
  allow: propTypes.bool.def(false),
  bgColor: propTypes.string.def(''),
  limit: propTypes.number.def(3),
  at: propTypes.bool.def(true),
  sharp: propTypes.bool.def(true),
  disabled: propTypes.bool.def(true)
})

/**
 * 获取评论列表
 */
const listEvent = async (id?: string | number) => {
  if (id) {
    queryParams.value.moduleId = props.category + id
  }
  queryParams.value.pageNo = 1
  await getList()
}

const getList = async () => {
  queryParams.value.pageSize = props.limit
  const res = await CommentApi.getCommentPage(queryParams.value)
  commentsList.value = res.list
  total.value = res.total
  // 确保视图更新
  await nextTick()
}

/**
 * 提交评论
 */
const commitComment = async (replay: boolean) => {
  if (!queryParams.value.moduleId) {
    message.error('未获取到对应ID，请刷新页面后重试')
    return
  }
  comment.value.moduleId = queryParams.value.moduleId
  if (replay) {
    comment.value.content = commentChildData.value.content
    buildImgUrls(commentChildData.value)
    comment.value.imgUrls = commentChildData.value.imgs
  } else {
    comment.value.content = commentData.value.content
    buildImgUrls(commentData.value)
    comment.value.imgUrls = commentData.value.imgs
    comment.value.parentId = -1
    comment.value.replyCommentId = -1
  }
  await CommentApi.createComment(comment.value)
  message.success('成功')
  listEvent()
  commentData.value = {
    content: '',
    imgs: ''
  }
  commentChildData.value = {
    content: '',
    imgs: ''
  }
}

const buildImgUrls = (data: CommentVO) => {
  if (typeof data.imgs == 'string') {
    if (!data.imgs) {
      data.imgs = []
    } else {
      data.imgs = [data.imgs]
    }
  }
}

/**
 * 点击评论按钮显示输入框
 * item: 当前大评论
 * reply: 当前回复的评论
 */
const replyComment = (item: CommentRespVO, reply: boolean) => {
  if (reply) {
    comment.value.replyCommentId = item.id
    comment.value.parentId = item.parentId
  } else {
    comment.value.parentId = item.id
    comment.value.replyCommentId = -1
  }
  commentReplayId.value = item.id
}

const delCommentEvent = async (item: any) => {
  await message.confirm('是否确认删除[' + item.content + ']？').finally()
  await CommentApi.deleteComment(item.id)
  await listEvent()
}

defineExpose({
  listEvent
})
</script>

<style scoped lang="scss">
:root {
  --animate-duration: 800ms;
  --animate-delay: 0.9s;
}
:deep(.iconfont) {
  font-size: 0.5rem !important;
  & > span {
    font-size: 0.5rem !important;
  }
  //margin-right: 5px;
}

:deep(.el-pagination) {
  --el-pagination-font-size-small: 0.6rem;
  --el-pagination-button-width-small: 1.2rem;
  --el-pagination-button-height-small: 1.2rem;
}
</style>
