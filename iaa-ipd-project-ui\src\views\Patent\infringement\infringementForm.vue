<template>
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      style="margin-top: 20px"
    >
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="提出人" prop="submittedPeople">
            <el-input v-model="formData.submittedPeople" placeholder="请输入提出人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户" prop="customers">
            <el-input v-model="formData.customers" placeholder="请输入客户" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品型号" prop="productModel">
            <TagsInput v-model="formData.productModel" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="附件" prop="submittedPeople">
            <UploadFile v-model="formData.attachmentIds" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="目标专利" prop="targetPatent">
            <el-input v-model="formData.targetPatent" placeholder="请输入目标专利" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="基本信息" prop="targetPatentInformation">
            <el-input
              v-model="formData.targetPatentInformation"
              placeholder="请输入目标专利基本信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="目标专利保护技术点" prop="targetPatentPoints">
            <el-input
              v-model="formData.targetPatentPoints"
              placeholder="请输入目标专利保护技术点"
            />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="目标专利附图" prop="targetPatentDrawings">
            <UploadFile v-model="formData.targetPatentDrawings" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="侵权分析报告" prop="analysisReport">
            <el-input v-model="formData.analysisReport" placeholder="请输入侵权分析报告" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="侵权分析报告（附件）" prop="analysisReportAttachment">
            <UploadFile v-model="formData.analysisReportAttachment" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="规避方案" prop="avoidanceScheme">
            <el-input
              v-model="formData.avoidanceScheme"
              placeholder="如侵权风险较高，合理的规避方案"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否继续项目" prop="isContinue">
            <el-radio-group v-model="formData.isContinue">
              <el-radio value="0" size="large">是</el-radio>
              <el-radio value="1" size="large">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利申请与布局" prop="avoidanceRemark">
            <el-input
              v-model="formData.avoidanceRemark"
              placeholder="规避方案的专利申请与布局"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="交叉许可" prop="crossLicensing">
            <el-input
              v-model="formData.crossLicensing"
              placeholder="与专利权人许可、交叉许可"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="跟进人" prop="followPerson">
            <el-input
              v-model="formData.followPerson"
              placeholder="规避方案跟进人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="规避方案专利申请号" prop="patentApplication">
            <el-input
              v-model="formData.patentApplication"
              placeholder="规避方案的专利申请与布局"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="30">
        <el-col :span="24">
          <el-form-item label="许可方式、费用及周期" prop="costsCycles">
            <el-input
              v-model="formData.costsCycles"
              placeholder="许可方式、费用及周期"
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { infringementApi } from '@/api/patent/infringement'
import UploadFile from '@/components/UploadFile/src/UploadFile.vue'
import { formatDate, parseDate } from '@/utils/formatTime'
import TagsInput from '../components/TagsInput.vue'
const visible = ref(false)
const formData = ref({
  id: undefined,
  submittedPeople: undefined,
  customers: undefined,
  productModel: [] as string[],
  attachmentIds: [] as string[],
  targetPatent: undefined,
  targetPatentInformation: undefined,
  targetPatentPoints: undefined,
  targetPatentDrawings: [] as string[],
  analysisReport: undefined,
  analysisReportAttachment: [] as string[],
  avoidanceScheme: undefined,
  isContinue: undefined,
  avoidanceRemark: undefined,
  crossLicensing: undefined,
  followPerson: undefined,
  costsCycles: undefined,
  patentApplication: undefined
})
const formRules = reactive({
  productModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
  targetPatentInformation: [{ required: true, message: '请输入目标专利基本信息', trigger: 'change' }],
  targetPatentPoints: [{ required: true, message: '请输入目标专利保护技术点', trigger: 'change' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const openForm = async (rows?: any[] | any) => {
  visible.value = true
  if (rows) {
    formType.value = 'update'
    dialogTitle.value = '修改产品专利侵权分析'
    const data = await infringementApi.getProductInfringement(rows)
    // 时间字段转换：时间戳 -> YYYY-MM-DD 字符串
    formData.value = {
      ...data
    }
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加产品专利侵权分析'
  }
}

const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  console.log(formData.value)
  try {
    if (formType.value === 'update') {
      await infringementApi.updateProductInfringement(formData.value)
      message.success('修改成功')
    } else {
      await infringementApi.createProductInfringement(formData.value)
      message.success('创建成功')
    }
    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
