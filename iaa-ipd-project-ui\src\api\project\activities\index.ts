import request from '@/config/axios'

// 项目活动 VO
export interface ActivitiesVO {
  id?: number // 活动ID
  basicsId?: number // 项目ID
  level?: number // 层级
  orderNo?: string // 版本号格式序号
  name?: string // 活动标题
  content?: string // 活动内容
  description?: string // 活动描述
  targetType?: number // 输出类型
  targetDockingId?: number // 对接ID
  targetTemplateIds?: number[] // 输出物模板Ids
  isCrux?: boolean // 是否关键活动
  director?: number[] // 负责人
  coordinate?: number[] // 执行人
  startDate?: string // 开始时间
  endDate?: string // 结束日期
  status?: number // 状态
  progress?: number // 进度
  mold?: number // 任务类型
  commentCount?: number //评论数量
  hasCompleted?: boolean // 是否允许完成活动
  dependencies?: any[] //依赖活动
  completedDate?: string
  approvalCompletedDate?: string
  currentProcessInstanceId?: string
}

export type ActivitiesDisassembleVO = {
  id?: number // 模板ID
  level?: number // 层级
  orderNo?: string // 版本号格式序号
  name?: string // 活动标题
  content?: string // 活动内容
  description?: string // 活动描述
  defaultRole?: string // 默认角色
  isCrux?: boolean // 是否关键活动
  director?: number[] // 负责人
  coordinate?: number[] // 其他配合人
  startDate?: string // 开始时间
  endDate?: string // 结束时间
  hasDisassemble?: boolean // 是否已分解
  hasMust?: boolean // 是否必须分解
  defaultManDay: number // 默认工时
}

// 项目活动 API
export const ActivitiesApi = {
  /** 获取项目活动列表 */
  getActivitiesList: (data: any) => {
    return request.post({ url: '/project/activities/list', data })
  },
  /** 获取项目活动分页 */
  getActivitiesPage: (data: any) => {
    return request.post({ url: '/project/activities/page', data })
  },
  /** 获取单个项目活动 */
  getActivities: (id: number) => {
    return request.get({ url: `/project/activities/get/${id}` })
  },
  /** 获取项目分解活动列表 */
  getActivitiesDisassembleList: (data: any) => {
    return request.post({ url: '/project/activities/list-disassemble', data })
  },
  /** 批量按模板分解活动 */
  createActivitiesBatch: (data: any) => {
    return request.post({ url: '/project/activities/create-batch', data })
  },
  /** 自定义分解活动 */
  createActivities: (data: any) => {
    return request.post({ url: '/project/activities/create', data })
  },
  /** 修改活动执行人 */
  updateActivitiesCoordinate: (data: any) => {
    return request.post({ url: '/project/activities/update-coordinate', data })
  },
  /** 导出活动 */
  exportActivities: (data: any) => {
    return request.downloadPost({ url: '/project/activities/export-excel', data })
  },
  /** 导出活动 */
  exportActivitiesMulti: (data: any) => {
    return request.downloadPost({ url: '/project/activities/export-multi', data })
  },
  /** 获取活动阶段数量 */
  getStageCount: (basicsId: number) => {
    return request.get({ url: `/project/activities/get-stage-count/${basicsId}` })
  },
  /** 获得关键节点信息分页 */
  getCruxInfoPage: (params: any) => {
    return request.get({ url: '/project/activities/get-crux-info-page', params })
  },
  /** 获取项目维度工时统计列表 */
  getWorkingHoursTotal: (basicsId: number) => {
    return request.get({ url: `/project/activities/get-working-hours-total/${basicsId}` })
  },
  /** 导出项目工时 */
  exportWorkingHours: (params: any) => {
    return request.download({ url: '/project/activities/export-crux-and-hours', params })
  },
  /** 检查项目的活动状态 */
  checkActivitiesStatus: (basicsId: number) => {
    return request.get({ url: `/project/activities/check-activities-status/${basicsId}` })
  }
}
