import request from '@/config/axios'

export interface OperationVO {
  id: any // 主键
  originalApplicant: any
  ownership: any
  applicationNo: any
  publicNo: any
  patentName: any
  protectionPoint: any
  licensorPeople: any
  licensorDate: any
  costs: any
  notice: any
  applicant: any
  beApplicant: any
  patentInvolved: any
  patentInvolvedName: any
  patentInvolvedPoint: any
  patentModel: any
  litigationSource: any
  analysisReport: any
  litigationSelect: any
  litigationCase: any
  entrustedLawyer: any
  trialDate: any
  litigationConclusion: any
}

export const OperationApi = {
  /** 分页获取知识产权运营 */
  getOperationPage: (data: any) => {
    return request.post({ url: '/patent/operation/page', data })
  },
  /** 创建知识产权运营 */
  createOperation: (data: OperationVO) => {
    return request.post({ url: '/patent/operation/create', data })
  },
  /** 更新知识产权运营 */
  updateOperation: (data: OperationVO) => {
    return request.post({ url: '/patent/operation/update', data })
  },
  /** 删除知识产权运营 */
  deleteOperation: (id: number) => {
    return request.get({ url: `/patent/operation/delete/${id}` })
  },
  /** 查询知识产权运营详情 */
  getOperation: async (id: any) => {
    return await request.get({ url: `/patent/operation/get?id=` + id })
  }
}
