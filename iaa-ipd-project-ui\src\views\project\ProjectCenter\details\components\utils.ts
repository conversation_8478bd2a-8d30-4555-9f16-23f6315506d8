import { getStrDictOptions } from '@/utils/dict'

// 需要字典转换的字段配置
export const DICT_FIELD_MAPPING = {
  platform: 'project_platform',
  mold: 'project_type',
  level: 'project_level',
  roleIds: 'project_team_role',
  problemLevel:'project_problem_level',
  problemCategory:'project_problem_category',
  problemModule:'project_problem_module',
  proposingDepartment:'project_problem_proposing_department',
  affect:'project_risk_level',
  probability:'project_risk_level',
  riskLevel:'project_risk_level',
  status:'project_risk_status'
} as const

// 判断是否需要显示字典标签
export const shouldShowDictTag = (field: string) => {
  return Object.keys(DICT_FIELD_MAPPING).includes(field)
}

// 格式化字典显示值
export const formatDictLabel = (field: keyof typeof DICT_FIELD_MAPPING, value: string) => {
  const dictType = DICT_FIELD_MAPPING[field]
  if (value?.startsWith('[')) {
    const ids = value.replace(/\[|\]|\"/g, '').split(',')
    return getStrDictOptions(dictType)
      .filter((item) => ids.includes(item.value))
      .map((item) => item.label)
      .join(',')
  }
  return getStrDictOptions(dictType).find((item) => item.value === value)?.label || value
}

/** 获取用户昵称 */
export const getUserNickName = (userList: any, ids: string | number[]) => {
  if (typeof ids === 'string' && !ids.replace(/^\[|\]$/g, '')) {
    return '空'
  }
  // 1. 处理字符串形式的数组（如 "[1,2,3]"）
  const tempIds: number[] =
    typeof ids === 'string'
      ? JSON.parse(ids) // 去除非数字和逗号/方括号字符
      : ids
  // 2. 确保元素是数字类型（兼容字符串数字如 "1"）
  const finalIds = Array.isArray(tempIds)
    ? tempIds.map((id) => parseInt(id as any)) // 强制转换并转为数字
    : []
  return userList
    .filter((item) => finalIds.includes(item.id))
    .map((item) => item.nickname)
    .join(',')
}
