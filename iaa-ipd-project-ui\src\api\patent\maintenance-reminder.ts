import request from '@/config/axios'

export const MaintenanceReminderApi = {
  /** 分页获取知识产权运营 */
  getMaintenanceReminderPage: (data: any) => {
    return request.post({ url: '/patent/maintenance-reminder/page', data })
  },
  /** 创建专利维护定时提醒 */
  createMaintenanceReminder: (data: any) => {
    return request.post({ url: '/patent/maintenance-reminder/create', data })
  },
  /** 更新专利维护定时提醒 */
  updateMaintenanceReminder: (data: any) => {
    return request.post({ url: '/patent/maintenance-reminder/update', data })
  },
  /** 删除专利维护定时提醒 */
  deleteMaintenanceReminder: (id: number) => {
    return request.get({ url: `/patent/maintenance-reminder/delete/${id}` })
  },
  /** 获得专利维护定时提醒 */
  getMaintenanceReminder: async (id: any) => {
    return await request.get({ url: `/patent/maintenance-reminder/get?id=` + id })
  }
}
