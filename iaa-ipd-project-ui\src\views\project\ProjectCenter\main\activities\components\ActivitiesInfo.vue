<template>
  <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
    <el-collapse-item title="基础信息" name="1">
      <el-form
        label-width="100px"
        class="custom-form"
        size="small"
        :model="formData"
        :rules="formRule"
        ref="formRef"
      >
        <el-form-item label="活动名称">
          <el-input v-model="formData.name" :disabled="true" />
        </el-form-item>
        <el-form-item label="活动内容">
          <el-input v-model="formData.content" type="textarea" :rows="3" :disabled="true" />
        </el-form-item>
        <el-form-item label="活动描述">
          <el-input v-model="formData.description" type="textarea" :rows="6" :disabled="true" />
        </el-form-item>
        <el-form-item label="负责人" prop="director">
          <user-avatar-list
            v-model="formData.director!"
            :user-list="props.userList"
            :size="28"
            :limit="3"
            :add="edit"
            :visiable-user-list="getVisiableUserList()"
            @change:msg="
              formDataChangeArray(
                'director',
                '活动负责人',
                formData.director!,
                props.data.director!,
                modifyInfo
              )
            "
          />
        </el-form-item>
        <el-form-item label="执行人">
          <user-avatar-list
            v-model="formData.coordinate!"
            :user-list="props.userList"
            :size="28"
            :limit="3"
            :add="edit"
            :visiable-user-list="[...getVisiableUserList(), ...supportlibraryList]"
            @change:msg="
              formDataChangeArray(
                'coordinate',
                '活动执行人',
                formData.coordinate!,
                props.data.coordinate!,
                modifyInfo
              )
            "
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                value-format="YYYY-MM-DD"
                class="!w-100%"
                :disabled="!edit"
                @change="
                  formDataChangeString(
                    'startDate',
                    '开始时间',
                    formData.startDate!,
                    props.data.startDate!,
                    modifyInfo
                  )
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                value-format="YYYY-MM-DD"
                class="!w-100%"
                :disabled="!edit"
                @change="
                  formDataChangeString(
                    'endDate',
                    '结束时间',
                    formData.endDate!,
                    props.data.endDate!,
                    modifyInfo
                  )
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际完成">
              {{ props.data.completedDate }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审签通过">
              {{ props.data.approvalCompletedDate }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="进度">
          <el-progress
            :percentage="formData.progress"
            class="w-100% no-radius"
            :text-inside="true"
            :stroke-width="20"
            status="success"
          />
        </el-form-item>
        <el-form-item label="状态">
          <DictTag type="project_activities_status" :value="formData.status!" />
        </el-form-item>
        <el-form-item label="里程碑">
          <DictTag type="infra_boolean_string" :value="formData.isCrux!" />
        </el-form-item>
      </el-form>
    </el-collapse-item>
    <el-collapse-item title="跟进记录" name="2" v-show="!edit">
      <Comment ref="commentRef" category="activities" :limit="5" bgColor="#fff" />
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts" setup>
import { UserVO } from '@/api/system/user'
import { ActivitiesVO } from '@/api/project/activities'
import { propTypes } from '@/utils/propTypes'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'
import { formDataChangeString, formDataChangeArray } from '@/utils/formDataChange'
import { getVisiableUserList } from '../../util/permission'
import { SupportLibraryApi } from '@/api/project/supportlibrary'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}

const activeNames = ref(['1', '2'])

const props = defineProps({
  userList: propTypes.oneOf<UserVO[]>([]).isRequired,
  data: propTypes.oneOf<ActivitiesVO>([]).isRequired,
  edit: propTypes.bool.def(false)
})

const formData = ref<ActivitiesVO>({})
const commentRef = ref()
const formRef = ref()
/** 数据修改 */
const modifyInfo = reactive<BasicsModifyInfoVO[]>([])

const formRule = reactive({
  startDate: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
    {
      validator: (_rule, value, callback) => {
        const endDate = formData.value.endDate
        validateDateRange(value, endDate!, callback)
      },
      trigger: 'change'
    }
  ],
  endDate: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (_rule, value, callback) => {
        const endDate = formData.value.endDate
        validateDateRange(value, endDate!, callback)
      },
      trigger: 'change'
    }
  ],
  director: [{ required: true, message: '请选择负责人', trigger: 'change' }]
})

const validateDateRange = (startVal: string, endVal: string, callback: Function) => {
  if (!startVal || !endVal) return callback()

  const start = new Date(startVal)
  const end = new Date(endVal)
  if (end < start) {
    callback(new Error('结束时间不能早于开始时间'))
  } else {
    callback()
  }
}

const getValue = async () => {
  await unref(formRef).validate()
  return {
    ...formData.value,
    modifyInfo
  }
}
const refreshComment = () => {
  commentRef.value?.listEvent(formData.value.id)
}

defineExpose({
  refreshComment,
  getValue
})

watch(
  () => props.data,
  () => {
    Object.assign(formData.value, props.data)
    getSupportLibraryList()
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__header) {
  background-color: #fafafa !important;
  border: 0.3px dashed var(--el-color-info-light-5) !important;
  border-left: 5px solid var(--el-color-primary) !important;
  font-size: 1rem;
  height: 1.8rem;
}
</style>
