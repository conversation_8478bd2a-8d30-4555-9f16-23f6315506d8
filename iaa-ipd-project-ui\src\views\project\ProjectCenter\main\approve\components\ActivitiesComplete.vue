<template>
  <el-tabs
    v-model="currentTab"
    class="position-sticky top-0 z-10 bg-#fff"
    @tab-change="onTableChange"
  >
    <el-tab-pane label="基础信息" name="info" />
    <el-tab-pane label="输出物" name="target" />
  </el-tabs>
  <template v-if="currentTab === 'info'">
    <el-form label-width="80px" class="custom-form" size="small" ref="formRef">
      <el-form-item label="所属项目">
        <el-input v-model="formData.basicsName" :disabled="true" />
      </el-form-item>
      <el-row>
        <el-col :span="8">
          <el-form-item label="等级">
            <el-input v-model="formData.basicsLevel" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型">
            <el-input v-model="formData.basicsMold" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="平台">
            <el-input v-model="formData.basicsPlatform" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="活动名称">
        <el-input v-model="formData.name" :disabled="true" />
      </el-form-item>
      <el-form-item label="活动内容">
        <el-input v-model="formData.content" type="textarea" :rows="3" :disabled="true" />
      </el-form-item>
      <el-form-item label="活动描述">
        <el-input v-model="formData.description" type="textarea" :rows="6" :disabled="true" />
      </el-form-item>
      <el-form-item label="负责人" prop="director">
        <user-avatar-list
          v-model="formData.director!"
          :user-list="userList"
          :size="28"
          :limit="3"
          :add="false"
        />
      </el-form-item>
      <el-form-item label="执行人">
        <user-avatar-list
          v-model="formData.coordinate!"
          :user-list="userList"
          :size="28"
          :limit="3"
          :add="false"
        />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="进度">
        <el-progress
          :percentage="formData.progress"
          class="w-100% no-radius"
          :text-inside="true"
          :stroke-width="20"
          status="success"
        />
      </el-form-item>
      <el-form-item label="状态">
        <DictTag type="project_activities_status" :value="formData.status!" />
      </el-form-item>
    </el-form>
    <Comment ref="commentRef" category="activities" :limit="5" bgColor="#fff" :disabled="false" />
  </template>
  <template v-else>
    <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
      <el-collapse-item
        title="输出参考"
        name="1"
        v-if="formData.mold == 0"
      >
        <div class="flex">
          <div
            class="cursor-pointer w-70px h-120px p-5px hover:bg-#f8f8f8"
            v-for="file in fileTemplateList.filter((item) =>
              formData.targetTemplateIds?.includes(item.id)
            )"
            :key="file.id"
            @click="officeEditorRef.open(file.infraFileId, file.name)"
          >
            <img :src="getImagePath(file.uri, file.hasFolder)" class="w-60px h-60px" />
            <div
              class="line-clamp-2 overflow-hidden [display:-webkit-box] [-webkit-box-orient:vertical] [-webkit-line-clamp:2] h-40px text-.65vw word-break-normal"
            >
              {{ file.name }}
            </div>
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item title="附件输出" name="2">
        <vxe-table
          class="w-100%"
          :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
          :cell-style="{ padding: '5px', height: '30px' }"
          show-overflow
          :data="attachmentList"
          align="center"
          border
        >
          <vxe-column title="文件名" field="name" min-width="200" align="left">
            <template #default="{ row }">
              <el-link
                type="primary"
                @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
              >
                {{ row.name }}
              </el-link>
            </template>
          </vxe-column>
          <vxe-column title="版本" field="currentVersion" width="60" />
          <vxe-column title="绑定参考" field="templateId" width="200">
            <template #default="{ row }">
              <el-select v-model="row.templateId" :disabled="true">
                <el-option
                  v-for="item in fileTemplateList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </template>
          </vxe-column>
          <vxe-column title="审签状态" field="approvalStatus" width="90">
            <template #default="{ row }">
              <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
            </template>
          </vxe-column>
          <vxe-column
            title="审核通过时间"
            field="approvalTime"
            :formatter="dateFormatter3"
            width="120"
          />
        </vxe-table>
      </el-collapse-item>
    </el-collapse>
    <FileUploadAnew
      category="activities"
      :dynamic-id="formData.id!"
      ref="fileUploadAnewRef"
      :file-template-list="
        fileTemplateList.filter((item) => formData.targetTemplateIds?.includes(item.id))
      "
      :target-type="formData.targetType"
      :target-docking-id="formData.targetDockingId"
      @success="onListAttachment"
    />
    <OfficeEditor ref="officeEditorRef" :download="true" />
    <AttachmentPreview ref="attachmentPreviewRef" />
  </template>
</template>
<script lang="ts" setup>
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { ActivitiesFlowApi } from '@/api/bpm/activities'
import { ActivitiesApi } from '@/api/project/activities'
import { propTypes } from '@/utils/propTypes'
import { getImagePath } from '@/utils/icon'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import { dateFormatter3 } from '@/utils/formatTime'
import { FileTemplateApi } from '@/api/project/file/template'
import AttachmentPreview from '../../components/AttachmentPreview.vue'

const commentRef= ref()
const currentTab = ref<string>('info')
const formData = ref<any>({})
const activitiesData = ref<any>({})
const userList = ref<UserVO[]>([])
const activeNames = ref(['1', '2'])
const fileTemplateList = ref<any[]>([])
const officeEditorRef = ref()
const attachmentList = ref<AttachmentRespVO[]>([])
const attachmentPreviewRef = ref()

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})
/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}
/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  if (currentTab.value === 'target') {
    onListAttachment()
  }
}

watch(
  () => props.processInstanceId,
  async () => {
    if (!props.processInstanceId) return
    formData.value = await ActivitiesFlowApi.getActivities(props.processInstanceId)
    getActivities(formData.value.activitiesId)
  },
  { immediate: true }
)

const getActivities = async (activitiesId: number) => {
  const res = await ActivitiesApi.getActivities(activitiesId)
  activitiesData.value = res
  onListComment()
}


const onListComment = async() =>{
  commentRef.value?.listEvent(activitiesData.value.id)
}
/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'activities',
    dynamicId: activitiesData.value.id
  })
  attachmentList.value = res
}

const listFileTemplate = async () => {
  const res = await FileTemplateApi.getFileTemplatePage({})
  fileTemplateList.value = res
}

onMounted(() => {
  getUserList()
  listFileTemplate()
})
</script>
