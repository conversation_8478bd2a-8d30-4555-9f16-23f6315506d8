<template>
  <ContentWrap>
    <div class="h-[calc(100vh-130px)]">
      <el-tabs
        type="border-card"
        class="header-tabs-container"
        v-model="activeTab"
      >
      <el-tab-pane label="专利维保" name="activities" style="height: 80vh">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" plain @click="maintenanceFormRef?.openForm()">
            新增
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          :header-cell-config="{ height: 40 }"
          :cell-config="{ height: 40 }"
          :data="list"
          @cell-click="cellClickEvent"
          border
          align="center"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column title="申请状态" field="status" width="90">
            <template #header>
              <div>申请状态</div>
              <el-select
                v-model="queryParams.status"
                @change="handleList"
                placeholder="选择申请状态"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_MAINTENANCE_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.PATENT_MAINTENANCE_STATUS" :value="row.status" />
            </template>
          </vxe-column>
          <vxe-column title="申请号" field="applicationNo" min-width="120">
            <template #header>
              <div>申请号</div>
              <el-input
                v-model="queryParams.applicationNo"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="公开号" field="publicNo" min-width="120">
            <template #header>
              <div>公开号</div>
              <el-input
                v-model="queryParams.publicNo"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="专利名称" field="patentName" min-width="120">
            <template #header>
              <div>专利名称</div>
              <el-input
                v-model="queryParams.patentName"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="当前权力人" field="ownership" min-width="120">
            <template #header>
              <div>当前权力人</div>
              <el-input
                v-model="queryParams.ownership"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="保护技术点" field="protectionPoint" min-width="120" />
          <vxe-column title="保护的产品型号" field="models" min-width="120" />
          <vxe-column title="专利类型" field="patentType" width="90">
            <template #header>
              <div>专利类型</div>
              <el-select
                v-model="queryParams.patentType"
                @change="handleList"
                placeholder="选择专利类型"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.PATENT_TYPE" :value="row.patentType" />
            </template>
          </vxe-column>
          <vxe-column
            title="申请日期"
            width="120"
            :formatter="dateFormatter4"
            field="applicationDate"
          >
            <template #header>
              <div>申请日期</div>
              <el-date-picker
                v-model="queryParams.applicationDate"
                type="date"
                placeholder="按回车筛选"
                @change="handleList"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column
            title="授权日期"
            width="120"
            :formatter="dateFormatter4"
            field="authorizationDate"
          >
            <template #header>
              <div>授权日期</div>
              <el-date-picker
                v-model="queryParams.authorizationDate"
                type="date"
                placeholder="按回车筛选"
                @change="handleList"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="有效期(年数)" field="validityPeriod" min-width="100" />
          <vxe-column title="获取方式" field="acquisitionMethod" min-width="120" />
          <vxe-column title="法律状态" align="left" field="legalStatus" width="90">
            <template #header>
              <div>法律状态</div>
              <el-select
                v-model="queryParams.legalStatus"
                @change="handleList"
                placeholder="选择法律状态"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.LEGAL_STATUS" :value="row.legalStatus" />
            </template>
          </vxe-column>
          <vxe-column title="代理机构" field="agency" min-width="120" />
          <vxe-column title="同族专利" field="families" min-width="120" />
          <vxe-column title="维保评估" field="maintenanceAssessment" min-width="120" />
          <!-- <vxe-column title="附件" field="attachmentIds" min-width="120" /> -->
          <vxe-column
            title="创建时间"
            min-width="150"
            field="createTime"
            :formatter="dateFormatter3"
          />
          <vxe-column title="操作" min-width="100" fixed="right">
            <template #default="{ row }">
              <el-button
                @click="maintenanceFormRef?.openForm(row.id)"
                link
                type="primary"
                v-hasPermi="['patent:maintenance:update']"
                >修改</el-button
              >
              <el-button
                @click="handleDelete(row.id)"
                link
                type="danger"
                v-hasPermi="['patent:maintenance:delete']"
                >删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      </el-tab-pane>
      <el-tab-pane label="专利提醒规则配置" name="problem" style="height: 80vh">
        <h1>嗨嗨嗨</h1>
      </el-tab-pane>
      </el-tabs>
    </div>
    <MaintenanceForm ref="maintenanceFormRef" @success="getList()" />
  </ContentWrap>
</template>
<script setup lang="ts">
import { MaintenanceApi, MaintenanceVO } from '@/api/patent/maintenance'
import MaintenanceForm from './MaintenanceForm.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter4, dateFormatter3 } from '@/utils/formatTime'

// 消息弹窗
const message = useMessage()
const maintenanceFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<MaintenanceVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  protectionPoint: undefined,
  status: undefined,
  patentType: undefined,
  legalStatus: undefined
})

// 默认选中"针对专利许可、转让"标签
const activeTab = ref('activities')
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const data = await MaintenanceApi.getMaintenancePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

//单击编辑
const cellClickEvent: any = ({ row }) => {
  maintenanceFormRef.value.openForm(row.id)
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await MaintenanceApi.deleteMaintenance(id)
  message.success('删除成功')
  await getList()
}

onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
/** header tabs 样式 */
:deep(.header-tabs-container) {
  border: none;
  border-radius: 2px;
  background-color: #ebeff3;

  & > .el-tabs__header {
    background-color: #fff !important;
    border-bottom: 0.3px solid #d8d8d8;
    // border-bottom-left-radius: 20px;
    // border-bottom-right-radius: 20px;

    .el-tabs__nav {
      padding: 0 20px;
    }
    .el-tabs__item {
      height: 2rem;
      font-size: 1rem;
      color: var(--primary-text-color);
      &.is-active {
        border-top: 3px solid var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }

  & > .el-tabs__content {
    padding: 0;
  }
}

</style>
