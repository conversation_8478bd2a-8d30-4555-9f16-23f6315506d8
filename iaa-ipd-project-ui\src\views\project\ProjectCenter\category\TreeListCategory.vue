<template>
  <div class="category-list">
    <div class="base-info" v-if="!treeListCategoryVisible">
      <div class="base-info-item base-info-item-name">
        <el-button type="primary" link @click="showHideCategory = !showHideCategory">
          {{ currentInfo?.name }}
        </el-button>
      </div>
      <div class="base-info-item">
        <span class="base-info-item-tip">等级：</span>
        <DictTag type="project_level" :value="currentInfo?.level!" />
      </div>
      <!-- <div class="base-info-item">
        <span class="base-info-item-tip">LPDT：</span>
        {{ getUserNickName([currentInfo?.teamLeader!]) }}
      </div> -->
      <div class="base-info-item">
        <span class="base-info-item-tip">PM：</span>
        {{ getUserNickName(currentInfo?.managers!) }}
      </div>
      <div class="base-info-item">
        <el-link type="primary" link @click="openForm('update', currentInfo?.id)">详情</el-link>
      </div>
      <div class="base-info-item">
        <el-link type="primary" @click="reduction"> 还原 </el-link>
      </div>
    </div>
    <div
      :class="{ 'category-list-menu-show': showHideCategory }"
      :style="{ display: treeListCategoryVisible ? 'block' : 'none', height: '100%' }"
    >
      <el-menu
        unique-opened
        menu-trigger="click"
        v-loading="loading"
        class="category-list-menu"
        :default-openeds="[defaultSubMenu]"
        :default-active="defaultActive"
        v-if="categoryList.length > 0 && viewCategoryList.length > 0"
        :key="menuKey"
      >
        <el-sub-menu
          v-for="(item, index) in categoryList.filter((item) => viewCategoryList.includes(item.id))"
          :key="`sub-${String(index)}`"
          :index="`sub-${String(item.id)}`"
          class="category-sub-menu"
          :title="item.name"
          @click="getBaseInfoList(item.id, `sub-${item.id}`, false)"
        >
          <template #title> {{ item.name }} </template>
          <template v-if="defaultSubMenu === `sub-${String(item.id)}`">
            <div
              v-for="project in baseInfoList"
              :key="`item-${String(project.id)}`"
              @click.stop="checkedProject(project.id!, item.id)"
            >
              <el-menu-item
                :index="`item-${String(project.id)}`"
                :class="{
                  'suspended-item': project.status === 1
                }"
              >
                <div
                  :class="{
                    ...calcReleaseDate(formatDate(project.releaseDate!)),
                    'project-item': true
                  }"
                >
                  <div class="project-item-name">
                    {{ project.name }}
                  </div>
                  <el-form inline size="small" class="base-info-form">
                    <el-form-item label="PM:">
                      {{ getUserNickName(project.managers!) }}
                    </el-form-item>
                    <el-form-item label="等级:">
                      {{
                        getStrDictOptions('project_level').find(
                          (dict) => dict.value === project.level
                        )?.label
                      }}
                    </el-form-item>
                    <el-form-item label="LPDT:">
                      {{
                        getUserNickName(
                          project.teams?.find((item) => item.role === 'lpdt')?.userIds || []
                        )
                      }}
                    </el-form-item>
                  </el-form>
                </div>
                <el-button
                  class="project-item-opertion"
                  type="primary"
                  link
                  @click.stop="openForm('update', project.id)"
                >
                  <Icon icon="ep:more" />
                </el-button>
              </el-menu-item>
            </div>
          </template>
        </el-sub-menu>
      </el-menu>
      <el-empty v-else description="暂无项目" />
      <div class="category-opertion-box">
        <!-- <el-button size="small" @click="emits('storage')" v-if="treeListCategoryVisible">
          <Icon icon="ep:upload" />
          收纳
        </el-button> -->
        <el-button
          type="primary"
          size="small"
          @click="openForm('create')"
          v-hasPermi="['project:basics:create']"
        >
          <Icon icon="ep:plus" />
          新建项目
        </el-button>
        <el-button type="primary" size="small" @click="searchVisible = true">
          <Icon icon="ep:search" />
          高级查询
        </el-button>
      </div>
    </div>
    <Dialog
      title="高级查询"
      v-model="searchVisible"
      destroy-on-close
      class="file-viewer-dialog"
      :close-on-click-modal="false"
    >
      <el-form label-width="120px" class="custom-form">
        <el-form-item label="项目名称">
          <el-input
            placeholder="请输入项目名称"
            v-model="queryParams.name"
            @keyup.enter.stop="handleSearch"
          />
        </el-form-item>
        <el-row>
          <el-col
            :span="12"
            v-for="dict in getStrDictOptions('project_team_role')"
            :key="dict.value"
          >
            <el-form-item :label="dict.label">
              <UserAvatarList
                v-model="queryParams.team[dict.value]"
                :user-list="userList.filter((item) => item.id !== 1)"
                :size="24"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="项目状态">
          <el-select v-model="queryParams.status" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="warning" @click="refreshQuery">清空筛选</el-button>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </template>
    </Dialog>
    <Dialog
      v-model="dialogVisible"
      width="80%"
      title="项目信息"
      destroy-on-close
      class="file-viewer-dialog"
      :close-on-click-modal="false"
      :before-close="onBeforeClose"
    >
      <BaseInfo
        v-model:baseId="baseId"
        v-model:formType="formType"
        @create:success="projectCreateSuccess(undefined, false)"
        @update:success="(businessKey: string) => projectCreateSuccess(businessKey, false)"
        ref="baseInfoRef"
        v-model:edit="formEdit"
      />
      <template
        #footer
        v-if="
          formType === 'update' &&
          baseInfo?.status !== 3 &&
          (baseInfo?.managers?.includes(getUser.id) || getUser.id === 1)
        "
      >
        <el-button type="success" v-if="baseInfo?.status !== 1" @click="onComplete">完结</el-button>
        <el-button type="danger" @click="deleteBasics">删除</el-button>
        <el-button type="warning" @click="pauseRestartBasics">
          {{ baseInfo?.status === 1 ? '重启' : '暂停' }}
        </el-button>
        <el-button type="primary" @click="initEditStatus" v-if="baseInfo?.status !== 1">
          {{ formEdit ? '保存' : '修改' }}
        </el-button>
      </template>
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import { CategoryApi, CategoryVO } from '@/api/project/category'
import { BasicsApi, BasicsVO } from '@/api/project/basics'
import { ActivitiesApi } from '@/api/project/activities'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import BaseInfo from '../details/index.vue'
import dayjs from 'dayjs'
import { getStrDictOptions, getIntDictOptions } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import { BasicsFlowVO, BasicsFlowApi } from '@/api/bpm/basics'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'

const props = defineProps({
  treeListCategoryVisible: {
    type: Boolean,
    default: false
  },
  currentId: {
    type: Number,
    default: undefined
  },
  currentCategory: {
    type: Number,
    default: undefined
  }
})
const showHideCategory = ref(false)
const defaultSubMenu = ref('') //当前分组的菜单
const defaultActive = ref('') //当前选中的菜单
const loading = ref(true) // 列表的加载中
const categoryList = ref<CategoryVO[]>([]) // 分类列表的数据
const baseInfoList = ref<BasicsVO[]>([])
const userList = ref<UserVO[]>([])
const dialogVisible = ref(false) //项目信息ID是否显示
const baseId = ref<number | undefined>(undefined) //项目ID
const baseInfo = ref<BasicsVO>()
const currentInfo = ref<BasicsVO>()
const formType = ref<string>('create')
const { push } = useRouter()
const message = useMessage() // 消息弹窗
const baseInfoRef = ref() //项目信息窗体
const formEdit = ref(false)
const { getUser } = useUserStore()
const searchVisible = ref(false)
const viewCategoryList = ref<number[]>([])
const menuKey = ref('')

const queryParams = ref({
  name: undefined,
  team: {} as any,
  status: [0, 2, 3, 4]
})

const refreshQuery = () => {
  queryParams.value = {
    name: undefined,
    team: {} as any,
    status: [0, 2, 3, 4]
  }
  handleQuery()
}

const handleQuery = async () => {
  const res = await BasicsApi.getQueryCategoryList(queryParams.value)
  viewCategoryList.value = res
  // if(viewCategoryList.value&&viewCategoryList.value.length>0){
  //   currentCategoryId.value = viewCategoryList.value[0]

  // }
  await onList()
}

const onList = async () => {
  if (!viewCategoryList.value.includes(currentCategoryId.value!)) {
    currentCategoryId.value = viewCategoryList.value[0]
  }
  const data = await BasicsApi.getBasicsList({
    categoryId: currentCategoryId.value,
    ...queryParams.value
  })
  baseInfoList.value = data
  searchVisible.value = false
}

const handleSearch = async () => {
  await handleQuery()
  if (baseInfoList.value && baseInfoList.value.length > 0) {
    defaultSubMenu.value = `sub-${currentCategoryId.value}`
    defaultActive.value = `item-${baseInfoList.value[0].id}`
    checkedProject(baseInfoList.value[0].id!, currentCategoryId.value!)
    menuKey.value = Math.random().toString()
  }
}
/** 查询列表 */
const getCategoryList = async () => {
  loading.value = true
  try {
    const data = await CategoryApi.getCategoryListByUserRange()
    categoryList.value = data
    if (
      props.currentCategory &&
      categoryList.value.some((item) => item.id === props.currentCategory)
    ) {
      defaultSubMenu.value = `sub-${props.currentCategory}`
      getBaseInfoList(props.currentCategory, defaultSubMenu.value, true)
      return
    }
    if (categoryList.value && categoryList.value.length > 0) {
      defaultSubMenu.value = `sub-${categoryList.value[0].id}`
      getBaseInfoList(categoryList.value[0].id, defaultSubMenu.value, true)
    }
  } finally {
    loading.value = false
  }
}

const currentCategoryId = ref<number>()
/** 查询当前分组项目列表 */
const getBaseInfoList = async (categoryId: number, subMenu: string, mouted: boolean) => {
  baseInfoList.value = []
  defaultSubMenu.value = subMenu
  loading.value = true
  currentCategoryId.value = categoryId
  try {
    await handleQuery()
    if (props.currentId && mouted) {
      defaultActive.value = `item-${props.currentId}`
      checkedProject(props.currentId, categoryId)
      return
    }
    if (currentInfo.value) {
      defaultActive.value = `item-${currentInfo.value.id}`
      checkedProject(currentInfo.value.id!, categoryId)
      return
    }
    if (baseInfoList.value && baseInfoList.value.length > 0 && !mouted) {
      defaultActive.value = `item-${baseInfoList.value[0].id}`
      checkedProject(baseInfoList.value[0].id!, categoryId)
    }
  } finally {
    loading.value = false
  }
}
/** 计算预计发布时间与当天比较情况 */
const calcReleaseDate = (date: string) => {
  let releaseDate = dayjs(date)
  let currentDate = dayjs()
  let days = releaseDate.diff(currentDate) / 1000 / 60 / 60 / 24
  if (days < 3 && days > 0) {
    return { 'project-warning': true }
  } else if (days < 0) {
    return { 'project-overdue': true }
  } else {
    return { 'project-normal': true }
  }
}
/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}
/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname)
    .join(',')
}
/** 项目流程创建成功 */
const projectCreateSuccess = async (businessKey?: string, toProcess?: boolean) => {
  getCategoryList()
  dialogVisible.value = false
  getBaseInfoList(currentCategoryId.value!, `sub-${currentCategoryId.value}`, false)
  if (toProcess) {
    await message.confirm('是否跳转到流程详情页面？')
    await push({ name: 'BpmProcessInstanceDetail', query: { id: businessKey } })
  }
}

/** 展示项目信息窗体 */
const openForm = (formTypeVal: string, projectId?: number) => {
  formType.value = formTypeVal
  baseId.value = projectId
  baseInfo.value = baseInfoList.value.find((item) => item.id === projectId)
  dialogVisible.value = true
  if (formTypeVal == 'create') {
    formEdit.value = true
  } else {
    formEdit.value = false
  }
}

/** 进入修改状态 */
const initEditStatus = async () => {
  if (formEdit.value) {
    await unref(baseInfoRef)?.saveEditData()
  } else {
    await unref(baseInfoRef)?.updateEditStatus(!formEdit.value)
  }
  formEdit.value = !formEdit.value
}

const emits = defineEmits(['checked:project', 'storage', 'reduction', 'checked:category:custom'])
const checkedProject = (projectId: number, categoryId: number) => {
  emits('checked:project', projectId, categoryId)
  currentInfo.value = baseInfoList.value.find((item) => item.id === projectId)
  const category = categoryList.value
    .filter((item) => currentInfo.value?.categoryIds?.includes(item.id))
    .filter((item) => item.templateCategory)
  emits('checked:category:custom', category[0]?.customActivity, category[0]?.templateCategory)
  showHideCategory.value = false
}

const onBeforeClose = (done: any) => {
  formEdit.value = false
  done()
}

const reduction = () => {
  showHideCategory.value = false
  emits('reduction')
}

const pauseRestartBasics = async () => {
  if (!baseId.value) return
  await BasicsApi.pauseRestartBasics(baseId.value)
  message.success('成功')
  getBaseInfoList(currentCategoryId.value!, `sub-${currentCategoryId.value}`, false)
  dialogVisible.value = false
}

const tempFormData = ref<BasicsVO>()
/** 删除项目 */
const deleteBasics = async () => {
  if (!baseId.value) return
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除项目', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  const res = await BasicsApi.getBasics(baseId.value)
  tempFormData.value = res
  if (!tempFormData.value) return

  const data = {
    basicsId: tempFormData.value.id,
    number: tempFormData.value.number,
    name: tempFormData.value.name,
    sort: tempFormData.value.sort,
    platform: tempFormData.value.platform,
    mold: tempFormData.value.mold,
    level: tempFormData.value.level,
    points: tempFormData.value.points,
    managers: tempFormData.value.managers,
    targetMarket: tempFormData.value.targetMarket,
    targetCustomer: tempFormData.value.targetCustomer,
    releaseDate: tempFormData.value.releaseDate,
    status: tempFormData.value.status,
    progress: tempFormData.value.progress,
    categoryIds: tempFormData.value.categoryIds,
    remark: tempFormData.value.remark,
    teams: tempFormData.value.teams
  } as BasicsFlowVO
  if (!data.modifyInfo) {
    data.modifyInfo = [] as BasicsModifyInfoVO[]
  }
  data.modifyInfo.push({
    modifyFieldName: '项目删除,原因：' + value,
    modifyField: 'update'
  })

  await BasicsFlowApi.deleteBasicsFlow(data)
  getBaseInfoList(currentCategoryId.value!, `sub-${currentCategoryId.value}`, false)
  dialogVisible.value = false
  message.success('项目删除流程启动成功，流程详情请前往流程详情页面查看')
}

const onComplete = async () => {
  loading.value = true
  try {
    await ActivitiesApi.checkActivitiesStatus(baseId.value!)
    const res = await BasicsApi.getBasics(baseId.value!)
    tempFormData.value = res
    if (!tempFormData.value) return

    const data = {
      basicsId: tempFormData.value.id,
      number: tempFormData.value.number,
      name: tempFormData.value.name,
      sort: tempFormData.value.sort,
      platform: tempFormData.value.platform,
      mold: tempFormData.value.mold,
      level: tempFormData.value.level,
      points: tempFormData.value.points,
      managers: tempFormData.value.managers,
      targetMarket: tempFormData.value.targetMarket,
      targetCustomer: tempFormData.value.targetCustomer,
      releaseDate: tempFormData.value.releaseDate,
      status: tempFormData.value.status,
      progress: tempFormData.value.progress,
      categoryIds: tempFormData.value.categoryIds,
      remark: tempFormData.value.remark,
      teams: tempFormData.value.teams
    } as BasicsFlowVO
    await BasicsFlowApi.completeBasicsFlow(data)
    getBaseInfoList(currentCategoryId.value!, `sub-${currentCategoryId.value}`, false)
    dialogVisible.value = false
    message.success('项目完结流程启动成功，流程详情请前往流程详情页面查看')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getCategoryList()
  getUserList()
})
</script>

<style lang="scss" setup>
.category-list {
  height: 100%;
  position: relative;
  background-color: #ebeff3;

  .base-info {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    font-size: 1rem;
    color: var(--regular-text-color);
    align-items: center;
    background-color: #fff;
    button {
      font-size: 1rem;
    }

    .base-info-item {
      font-size: 0.75vw !important;

      .el-link {
        font-size: 0.75vw;
      }
    }

    .base-info-item-tip {
      font-size: 0.7vw;
      color: var(--disabled-text-color);
      padding-left: 3px;
    }

    .base-info-item-name {
      .el-button {
        font-weight: bold;
        padding: 0 25px;
        // background-color: var(--el-color-primary-light-9);
      }
    }
  }

  .category-opertion-box {
    position: absolute;
    bottom: 0;
    height: calc(6rem + 10px);
    width: 100%;
    display: flex;
    flex-direction: column-reverse;
    justify-content: flex-start;
    align-items: center;
    background-color: #ebeff3;
    .el-button {
      width: 80%;
      height: 2rem !important;
      font-size: 1rem;

      i {
        font-size: 1rem !important;
        span {
          font-size: 1rem !important;
        }
      }
    }

    .el-button + .el-button {
      margin-left: 0 !important;
      margin-bottom: 5px;
    }
  }
}

.category-list-menu {
  max-height: calc(100% - 6rem - 10px);
  overflow-y: scroll;
  overflow-x: hidden;
  border-right: none;

  .category-sub-menu .el-sub-menu__title {
    height: 2.5rem !important;
    line-height: 100%;
    color: var(--secondary-text-color) !important;
    font-size: 1rem;
    font-weight: 550;
    padding: 3px 10px !important;
    box-sizing: border-box;
    box-shadow:
      inset 0 -0.5px 0 #b3b3b3,
      /* 较深的底部阴影 */ inset 0 0.5px 0 #f5f5f5; /* 较浅的顶部阴影 */
    background-color: #ebeff3;
    &:hover {
      color: var(--el-color-primary) !important;
      background-color: #fff !important;
    }

    .el-icon {
      margin-right: -15px;
      transform: rotateZ(-90deg) !important;
    }
  }

  /** 菜单滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #ebeff3;
  }

  &::-webkit-scrollbar-thumb {
    background: #dadadaad; /* 滚动条滑块背景 */
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #adadad; /* 滚动条滑块悬停背景 */
  }

  .category-sub-menu {
    &.is-opened {
      .el-sub-menu__title {
        color: var(--el-color-primary) !important;

        .el-icon {
          transform: rotateZ(0deg) !important;
        }
      }
    }

    .el-menu-item {
      height: 3.5rem !important;
      line-height: 3rem;
      padding-left: 10px !important;
      padding: 0 0 0 10px;
      background-color: #fff;
      justify-content: space-between;

      .el-button > span {
        font-size: 0.8rem;

        & > i {
          font-size: 0.8rem !important;
          span {
            font-size: 0.8rem !important;
          }
        }
      }

      &.is-active,
      &:hover {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary) !important;
        .project-item-name {
          color: var(--el-color-primary) !important;
        }
        .base-info-form {
          color: var(--el-color-primary-light-6) !important;
          .el-form-item__label {
            color: var(--el-color-primary-light-6) !important;
          }
        }
      }
      &.is-active {
        border-right: 3px solid var(--el-color-primary);
      }

      .project-item {
        height: 3.5rem;
        padding: 0 0 0 15px;
        width: 85%;
        overflow: hidden;
        border-bottom: 0.3px dashed var(--disabled-text-color);

        //border-radius: 3px;

        .project-item-name {
          height: 2rem;
          line-height: 2rem;
          font-size: 0.9rem;
          color: var(--primary-text-color);
          // letter-spacing: 2px;
        }

        .base-info-form {
          height: 1.5rem;
          line-height: 1.5rem;
          font-weight: lighter;
          .el-form-item {
            margin-bottom: 0px;
            margin-right: 5px;
            height: 1.5rem !important;
            line-height: 1.5rem !important;

            & > * {
              height: 1.5rem !important;
              line-height: 1.5rem !important;
            }

            .el-form-item__label {
              padding: 0 2px 0 0;
              color: var(--secondary-text-color);
              font-size: 0.8rem;
            }

            .el-form-item__content {
              font-size: 0.8rem;
              color: var(--secondary-text-color);
              padding: 0 2px 0 0;
            }
          }
        }
      }

      .project-item-opertion {
        width: 15%;
        height: 3rem;
        line-height: 3rem;

        .el-button {
          height: 3rem;
          line-height: 3rem;
        }
      }
    }

    .suspended-item {
      &.is-active,
      &:hover {
        background-color: var(--el-color-warning-light-9) !important;
      }

      &.is-active {
        border-right: 3px solid var(--el-color-warning) !important;
      }
    }
  }
}

.category-list-menu-show {
  position: absolute;
  display: block !important;
  top: 20px;
  left: -2px;
  z-index: 11;
  height: 80vh !important;
  background-color: #ebeff3;
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.2),
    0 6px 6px rgba(0, 0, 0, 0.1);
}

.project-warning {
  border-left: 2px solid var(--el-color-warning);
}
.project-overdue {
  border-left: 2px solid var(--el-color-danger);
}
// .project-normal {
//   border-left: 2px solid var(--el-color-success);
// }
</style>
