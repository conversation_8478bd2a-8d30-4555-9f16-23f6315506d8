<template>
  <el-form
    label-width="90px"
    class="custom-border-form custom-form"
    :rules="formRulesBase"
    :model="formData"
    ref="formRef"
  >
    <el-form-item label="项目名称" prop="name">
      <el-input
        :maxlength="64"
        show-word-limit
        v-model="formData.name"
        :disabled="!edit"
        @change="formDataChange('name', '项目名称', formData.name!, propFormData.name!)"
      />
    </el-form-item>
    <el-form-item label="项目编号" prop="number">
      <el-input :maxlength="64" show-word-limit v-model="formData.number" :disabled="true" />
    </el-form-item>
    <el-form-item label="项目经理" prop="managers">
      <el-select
        filterable
        multiple
        placeholder="请选择项目经理，可选多个"
        v-model="formData.managers"
        :disabled="!edit"
        @change="
          formDataChange(
            'managers',
            '项目经理',
            JSON.stringify(formData.managers!),
            JSON.stringify(propFormData.managers!)
          )
        "
      >
        <el-option
          v-for="item in userList.filter((user) => user.id !== 1)"
          :key="item.id"
          :label="item.nickname"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="项目组" prop="categoryIds">
      <el-select
        multiple
        placeholder="请选择项目组，可选多个"
        v-model="formData.categoryIds"
        :disabled="!edit"
      >
        <el-option
          v-for="item in categoryList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-row>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="发布时间" prop="releaseDate">
          <el-date-picker
            type="date"
            value-format="YYYY-MM-DD"
            v-model="formData.releaseDate"
            :disabled="!edit"
            :disabled-date="disabledDate"
            @change="
              formDataChange(
                'releaseDate',
                '发布时间',
                formatDate(formData.releaseDate!),
                formatDate(propFormData.releaseDate!)
              )
            "
          />
        </el-form-item>
      </el-col>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="项目平台" prop="platform">
          <el-select
            v-model="formData.platform"
            :disabled="!edit"
            @change="
              formDataChange('platform', '项目平台', formData.platform!, propFormData.platform!)
            "
          >
            <el-option
              v-for="dict in getStrDictOptions('project_platform')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="项目类型" prop="mold">
          <el-select
            v-model="formData.mold"
            @change="
              () => {
                onMoldChangeEvent()
                formDataChange('mold', '项目类型', formData.mold!, propFormData.mold!)
              }
            "
            :disabled="!edit"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="项目等级" prop="level">
          <el-select
            v-model="formData.level"
            @change="
              () => {
                onLevelChangeEvent()
                formDataChange('level', '项目等级', formData.level!, propFormData.level!)
              }
            "
            :disabled="!edit"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_LEVEL)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="客户" prop="targetCustomer">
          <el-input
            :maxlength="64"
            show-word-limit
            v-model="formData.targetCustomer"
            :disabled="!edit"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="市场" prop="targetMarket">
          <el-input
            :maxlength="64"
            show-word-limit
            v-model="formData.targetMarket"
            :disabled="!edit"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="项目排序" prop="sort">
          <el-input-number :min="1" :max="50" v-model="formData.sort" :disabled="!edit" />
        </el-form-item>
      </el-col>
      <el-col :span="6" :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-form-item label="项目积分">
          <el-input-number :min="1" v-model="formData.points" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-form-item label="计划下单">
          <el-date-picker
            type="date"
            value-format="YYYY-MM-DD"
            v-model="formData.planOrderDate"
            :disabled="!edit"
            :disabled-date="disabledDate"
            @change="
              formDataChange(
                'planOrderDate',
                '计划下单时间',
                formatDate(formData.planOrderDate!),
                formatDate(propFormData.planOrderDate!)
              )
            "
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-form-item label="实际下单">
          <el-date-picker
            type="date"
            value-format="YYYY-MM-DD"
            v-model="formData.actualOrderDate"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="备注">
      <el-input type="textarea" :rows="12" v-model="formData.remark" :disabled="!edit" />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { DICT_TYPE, getStrDictOptions, getIntDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user/index'
import { CategoryVO } from '@/api/project/category'
import { BasicsVO } from '@/api/project/basics'
import { copyValueToTarget } from '@/utils'
import { EncoderApi } from '@/api/infra/encoder'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'
import { formatDate } from '@/utils/formatTime'
import { dateUtil } from '@/utils/dateUtil'
import * as ConfigApi from '@/api/infra/config'

const message = useMessage() // 消息弹窗

const props = defineProps({
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired,
  categoryList: propTypes.oneOfType([Array<CategoryVO>]).isRequired,
  activeName: propTypes.string.isRequired,
  propFormData: {
    type: Object as PropType<BasicsVO>,
    default: () => {}
  },
  edit: propTypes.bool.def(true),
  formType: propTypes.string.def('create')
})

const formRef = ref()
// 表单修改记录
const modifyInfo = ref<BasicsModifyInfoVO[]>([])

/** 表单基础数据 */
const formData = ref<BasicsVO>({
  id: undefined, //项目id
  number: undefined, // 项目编号
  name: undefined, // 项目名称
  sort: undefined, // 显示顺序
  platform: undefined, // 平台
  level: undefined, // 项目等级
  points: undefined, // 项目积分
  managers: undefined, // 项目经理ids
  targetMarket: undefined, // 针对市场
  targetCustomer: undefined, // 针对客户
  mold: undefined, // 项目类型(0:自主研发，1:客户定制)
  releaseDate: undefined, // 产品预计发布时间
  planOrderDate: undefined,
  actualOrderDate: undefined,
  remark: undefined, // 备注
  status: undefined, // 项目状态（0:进行中，1:暂停，2:完结）
  progress: undefined, // 项目进度
  categoryIds: undefined //项目分组ids
})

const isRequired = computed(() => {
  if (formData.value.categoryIds?.length == 0) {
    return false
  }
  if (formData.value.categoryIds?.includes(16)) {
    return false
  }
  const tempCategoryList = props.categoryList.filter((category) =>
    formData.value.categoryIds?.includes(category.id)
  )
  const templateCategoryCount = tempCategoryList
    .filter((category) => category.templateCategory)
    .map((category) => category.templateCategory).length
  if (templateCategoryCount > 1) {
    return true
  }
  return !formData.value.categoryIds?.includes(4)
})

const formRulesBase = ref<any>({
  name: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '请填写项目显示顺序', trigger: 'blur' }],
  level: [{ required: true, message: '请选择项目等级', trigger: 'change' }],
  managers: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
  platform: [{ required: isRequired, message: '请选择项目平台', trigger: 'change' }],
  mold: [{ required: isRequired, message: '请选择项目类型', trigger: 'change' }],
  releaseDate: [{ required: true, message: '请选择产品预计发布时间', trigger: 'blur' }],
  categoryIds: [{ required: true, message: '请至少选择一个项目组', trigger: 'change' }]
})

/** 项目类型变更事件 */
const onMoldChangeEvent = async () => {
  let encodeId = 1

  // 删除现有的验证规则
  delete formRulesBase.value.targetCustomer
  delete formRulesBase.value.targetMarket

  // 根据 mold 的值设置 encodeId 和验证规则
  if (formData.value.mold == '20') {
    encodeId = 2
  } else {
    formRulesBase.value['targetMarket'] = [
      { required: true, message: '市场信息必填', trigger: 'blur' }
    ]
  }

  // 设置 targetCustomer 的验证规则
  formRulesBase.value['targetCustomer'] = [
    { required: true, message: '客户信息必填', trigger: 'blur' }
  ]

  // 生成编码
  formData.value.number = await EncoderApi.generateEncode({
    encodeId: encodeId
  })
}

const onLevelChangeEvent = async () => {
  const level = getStrDictOptions('project_level').find(
    (dict) => dict.value === formData.value.level
  )
  const point = await getIntDictOptions('project_level_points').find(
    (dict) => dict.label === level?.label
  )
  formData.value.points = point?.value
}

/** 校验项目组合理性 */
const onCheckCategoryEvent = (): boolean => {
  const tempCategoryList = props.categoryList.filter((category) =>
    formData.value.categoryIds?.includes(category.id)
  )
  if (tempCategoryList.length === 0) {
    message.alertError('请选择分类')
    return false
  }
  const allTemplateCategoriesEmpty = tempCategoryList.every((category) => {
    return !category.templateCategory
  })
  if (allTemplateCategoriesEmpty) {
    message.alertError(
      'TOP 10项目、生命周期项目为关注类分组，不能单独或同时存在。请在选择这两个分组时同时选择其他分组'
    )
    return false
  }
  const templateCategoryCount = tempCategoryList
    .filter((category) => category.templateCategory)
    .map((category) => category.templateCategory).length
  if (templateCategoryCount > 1) {
    message.alertError('IOT项目、预研项目与其他项目因项目模板不同，不能同时存在，请检查')
    return false
  }
  return true
}

/** 项目信息变更 */
const formDataChange = (key: string, title: string, currentValue: string, beforeValue: string) => {
  if (currentValue == beforeValue) {
    modifyInfo.value = modifyInfo.value.filter((item) => item.modifyField !== key)
    return
  }
  const exists = modifyInfo.value.find((item) => item.modifyField === key)
  if (exists) {
    exists.beforeValue = beforeValue
    exists.afterValue = currentValue
  } else {
    modifyInfo.value.push({
      modifyField: key,
      modifyFieldName: title,
      beforeValue: beforeValue,
      afterValue: currentValue
    })
  }
}

/** 表单校验 */
const emit = defineEmits(['update:activeName'])
const validate = async () => {
  if (!formRef) return
  try {
    await unref(formRef).validate()
    if (!onCheckCategoryEvent()) {
      return
    }
    //校验通过更新数据
    Object.assign(props.propFormData, formData.value)
  } catch (e) {
    message.error('【项目基础信息】不完善，请填写相关信息')
    emit('update:activeName', 'info')
    throw e
  }
}
/** 刷新表单数据 */
const refreshBase = () => {
  Object.assign(props.propFormData, formData.value)
}
/** 获取表单变更数据 */
const getChangeMsg = () => {
  return modifyInfo.value
}

defineExpose({ validate, refreshBase, getChangeMsg })

const iotDate = ref(0)
const getIotDateConfig = async () => {
  const res = await ConfigApi.getConfigKey('iot.date')
  iotDate.value = res
}
/** 将ga时间之后的时间禁用，同时将同行第一个时间之前的时间禁用 */
const disabledDate = (time: Date) => {
  if (formData.value.categoryIds?.includes(8) && iotDate.value == 1) {
    return false
  }
  return dateUtil().subtract(1, 'days').isAfter(time)
}

/** 将传进来的值赋值给formData */
watch(
  () => props.propFormData,
  (data) => {
    if (!data) {
      return
    }
    getIotDateConfig()
    copyValueToTarget(formData.value, data)
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
:deep(.el-input),
:deep(.el-input-number) {
  width: 100%;
}
</style>
