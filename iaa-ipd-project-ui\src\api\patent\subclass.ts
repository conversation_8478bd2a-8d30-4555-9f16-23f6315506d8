import request from '@/config/axios'

export const SubclassApi = {
    /** 查询子表信息 */
  querySubclass: (data: any) => {
    return request.post({ url: '/patent/subclass/querySubclass', data })
  },
  /** 创建子表信息 */
  batchCreate: (data: any) => {
    return request.post({ url: '/patent/subclass/batchCreate', data })
  },
  /** 修改子表信息 */
  batchUpdate: (data: any) => {
    return request.post({ url: '/patent/subclass/batchUpdate', data })
  },
}
