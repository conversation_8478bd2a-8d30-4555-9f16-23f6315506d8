<template>
  <ContentWrap>
    <el-form inline class="-mb-15px">
      <el-form-item label="月份">
        <el-date-picker
          v-model="currentDate"
          type="month"
          value-format="YYYY-MM-DD"
          :clearable="false"
          @change="getUserList"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="uploadFormRef?.open()">导入数据</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <div class="h-[calc(100vh-250px)] w-full flex">
      <vxe-split class="wh-full">
        <vxe-split-pane width="200px" min-width="200px">
          <div class="wh-full overflow-auto text-center" v-if="userList.length > 0">
            <div
              v-for="user in userList"
              :key="user.value"
              :class="['user-item', 'p-10px', currentUser == user.value ? 'checked' : '']"
              @click="getHoursList(user.value)"
            >
              {{ user.key }}
            </div>
          </div>
          <el-empty description="本月暂无工时数据" v-else />
        </vxe-split-pane>
        <vxe-split-pane>
          <el-calendar
            :range="[new Date(currentDate), dateUtil(currentDate).endOf('months').toDate()]"
          >
            <template #header>
              <CardTitle title="日历" />
              <span>{{ dayjs(currentDate).format('YYYY年MM月') }}</span>
            </template>
            <template #date-cell="{ data }">
              <template v-if="isCurrentMonth(data.day)">
                <div class="position-relative">
                  {{ data.day.split('-').slice(2).join('-') }}
                </div>
                <div
                  class="w-full h-50% flex items-center justify-center text-center text-1.2rem text-[var(--el-color-primary)]"
                  v-if="hoursList.find((item) => item.day === data.day)?.hours"
                >
                  {{ hoursList.find((item) => item.day === data.day)?.hours || 0 }}
                </div>
              </template>
            </template>
          </el-calendar>
        </vxe-split-pane>
      </vxe-split>
    </div>
  </ContentWrap>
  <UploadForm v-model:date="currentDate" ref="uploadFormRef" @success="getUserList()" />
</template>

<script lang="ts" setup>
import { dateUtil } from '@/utils/dateUtil'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
import { ActualAttendanceHoursApi } from '@/api/project/actualattendancehours'
import UploadForm from './UploadForm.vue'

const currentDate = ref(dateUtil().startOf('months').format('YYYY-MM-DD'))
const uploadFormRef = ref()

const loading = ref(false)
const userList = ref<any[]>([])
const currentUser = ref()
const hoursList = ref<any[]>([])

const getUserList = async () => {
  loading.value = true
  try {
    const res = await ActualAttendanceHoursApi.getUserList(currentDate.value)
    userList.value = res
  } finally {
    loading.value = false
  }
}

// 在 script setup 中定义
const isCurrentMonth = (dateStr: string) => {
  const targetMonth = dayjs(dateStr).month() // 获取目标日期的月份（0-11）
  const currentMonth = dayjs(currentDate.value).month() // 获取当前选中月份
  return targetMonth === currentMonth
}

const getHoursList = async (userId: number) => {
  loading.value = true
  try {
    currentUser.value = userId
    hoursList.value = await ActualAttendanceHoursApi.getHoursList(userId, currentDate.value)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getUserList()
})
</script>

<style lang="scss" scoped>
.user-item {
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
}

.user-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.user-item.checked {
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-calendar__body) {
  // 仅保留当前日期单元格
  .el-calendar-table:nth-child(1) .el-calendar-table__row td.current {
    border: none;
    span {
      display: none;
    }
  }

  // 特殊处理第二个表格
  .el-calendar-table:nth-child(2) .el-calendar-table__row td.next {
    display: none;
  }
}
</style>
