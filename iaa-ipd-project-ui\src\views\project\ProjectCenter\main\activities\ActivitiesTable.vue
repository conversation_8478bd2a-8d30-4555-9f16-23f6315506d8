<template>
  <!-- <vxe-toolbar ref="toolbarRef" custom export /> -->
  <vxe-table
    ref="activitiesTableRef"
    height="91%"
    :header-cell-style="{
      padding: '0',
      height: '2.5rem',
      fontSize: '.9rem',
      backgroundColor: '#fafafa',
      color: 'var(--primary-text-color)'
    }"
    :row-style="{
      cursor: 'pointer'
    }"
    :cell-style="{
      padding: '0',
      height: '2.5rem',
      fontSize: '.9rem',
      color: 'var(--primary-text-color)'
    }"
    :export-config="{ remote: true, message: false, exportMethod: onExport }"
    round
    border
    auto-resize
    :row-config="{ isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
    :column-config="{ resizable: true, isHover: true }"
    :data="props.list"
    show-overflow
    :loading="loading"
    @cell-click="(el: any) => emits('show', el.row)"
  >
    <vxe-column
      title="活动主题"
      field="name"
      width="14%"
      :filters="nameOptions"
      :filter-render="FilterValue.textFilterRender"
    >
      <template #default="{ row }">
        <div :style="{ width: `${(row.level - 1) * 10}px`, display: 'inline-block' }"> </div>
        {{ `${row.orderNo}  ${row.name}` }}
      </template>
    </vxe-column>
    <vxe-column title="进度" field="progress" align="center" width="7%">
      <template #default="{ row }">
        <el-progress
          :text-inside="true"
          :stroke-width="userSize - 6"
          :percentage="row.progress"
          status="success"
          class="no-radius"
        />
      </template>
    </vxe-column>
    <vxe-column
      title="状态"
      field="status"
      align="center"
      width="6%"
      :filters="FilterValue.statusOptions"
    >
      <template #default="{ row }">
        <dict-tag :type="DICT_TYPE.PROJECT_ACTIVITIES_STATUS" :value="row.status" />
      </template>
    </vxe-column>
    <vxe-column
      title="活动内容"
      width="15%"
      field="content"
      :filters="contentOptions"
      :filter-render="FilterValue.textFilterRender"
    />
    <vxe-column
      title="活动描述"
      min-width="24%"
      field="description"
      :filters="descriptionOptions"
      :filter-render="FilterValue.textFilterRender"
    />
    <vxe-column title="时间" align="center" field="date" width="12%">
      <template #default="{ row }">
        {{ formatToDate(row.startDate, 'YYYY.MM.DD') }}-{{
          formatToDate(row.endDate, 'YYYY.MM.DD')
        }}
      </template>
    </vxe-column>
    <vxe-column
      title="负责人"
      width="10%"
      field="director"
      :filters="directorOptions"
      :filter-render="FilterValue.userFilterRender"
    >
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.director"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="false"
          @click.stop
        />
      </template>
    </vxe-column>
    <vxe-column
      title="执行人"
      width="10%"
      field="coordinate"
      :filters="coordinateOptions"
      :filter-render="FilterValue.userFilterRender"
    >
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.coordinate"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="![3, 4, 10, 11].includes(row.status) && row.director.includes(userStore.getUser.id)"
          :visiable-user-list="[...getVisiableUserList(), ...supportlibraryList]"
          @click.stop
          @change:msg="onCoordinateChange(row)"
        />
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script lang="ts" setup>
import { ActivitiesVO, ActivitiesApi } from '@/api/project/activities'
import { DICT_TYPE } from '@/utils/dict'
import { formatToDate } from '@/utils/dateUtil'
import { UserVO } from '@/api/system/user'
import { PropType } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { propTypes } from '@/utils/propTypes'
import download from '@/utils/download'
import { useCache } from '@/hooks/web/useCache'
import { getVisiableUserList } from '../util/permission'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'
import { SupportLibraryApi } from '@/api/project/supportlibrary'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}

const nameOptions = ref([{ data: '' }])
const contentOptions = ref([{ data: '' }])
const descriptionOptions = ref([{ data: '' }])
const directorOptions = ref([{ data: [] }])
const coordinateOptions = ref([{ data: [] }])

// const toolbarRef = ref()
const activitiesTableRef = ref()
const userStore = useUserStore()
const message = useMessage()
const { wsCache } = useCache()
const loading = ref(false)

const props = defineProps({
  list: {
    type: Array as PropType<ActivitiesVO[]>,
    default: () => []
  },
  userList: {
    type: Array as PropType<UserVO[]>,
    default: () => []
  },
  toolbar: propTypes.any.isRequired,
  basicsId: propTypes.number.isRequired
})

const setCurrent = async (id: number) => {
  requestAnimationFrame(() => {
    const row = activitiesTableRef.value?.getRowById(id)
    activitiesTableRef.value?.scrollToRow(row)
    activitiesTableRef.value?.setCurrentRow(row)
    emits('show', row)
    wsCache.delete('project_page_show_form')
  })
}

defineExpose({ setCurrent })

const emits = defineEmits(['show', 'success'])

/** 执行人变更 */
const onCoordinateChange = async (row: ActivitiesVO) => {
  loading.value = true
  try {
    row.director?.forEach((item) => {
      if (row.coordinate?.includes(item)) {
        const userInfo = props.userList.find((user) => user.id === item)
        message.warning(`${userInfo?.nickname}已经是当前活动的负责人了，不能添加为执行人`)
        row.coordinate = row.coordinate.filter((id) => id !== item)
      }
    })
    if (row.coordinate && row.coordinate.length > 0) {
      await ActivitiesApi.updateActivitiesCoordinate(row)
      message.success(`修改成功`)
      emits('success', row.id)
    }
  } finally {
    loading.value = false
  }
}

const onExport = async ({ options }: any) => {
  try {
    if (!props.basicsId) return
    console.log(options)
    // 导出的二次确认
    await message.exportConfirm()
    const columns = options.columns.map((item) => item.field)
    if (columns.length === 0) {
      message.warning('未选择需要导出的列')
      return
    }
    if (columns.includes('date')) {
      columns.splice(columns.indexOf('date'), 1, 'startDate', 'endDate')
    }
    const data = await ActivitiesApi.exportActivities({
      basicsId: props.basicsId,
      filename: options.filename,
      column: columns
    })
    download.excel(data, `${options.filename}.xlsx`)
  } catch {}
}

/** 监听改变用户组件大小 */
const _windowWidth = ref(28)
const userSize = computed({
  get: () => _windowWidth.value,
  set: (value) => (_windowWidth.value = value)
})

const handleResize = () => {
  const decimalPercentage = 1.6 / 100
  _windowWidth.value = window.innerWidth * decimalPercentage
}

onMounted(() => {
  nextTick(() => {
    unref(activitiesTableRef)?.connect(props.toolbar)
  })
  handleResize()
  window.addEventListener('resize', handleResize)
  getSupportLibraryList()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
:deep(.vxe-body--expanded-column) {
  width: 98%;
}

:deep(.vxe-cell) {
  padding: 0 5px;
}

:deep(.el-tag) {
  --el-tag-font-size: 0.9rem;
  height: 1.5rem;
}

:deep(.vxe-body--row.is--expand-row.row--current) {
  & + .vxe-body--expanded-row {
    .expand-container {
      box-shadow:
        inset 4px 4px 0 0 rgba(163, 226, 255, 0.404),
        /* 外阴影，右下 */ inset -4px -4px 0 0 rgba(163, 226, 255, 0.404); /* 内阴影，左上 */
    }
  }
}

.expand-container {
  background-color: #ebeff3;
  // min-height: 100px;
  overflow: auto;
  display: flex;
  padding: 5px 30px;

  .comment-container {
    width: 100%;
  }

  // .file-container {
  //   width: 20%;
  // }
}
:deep(.vxe-body--row.row--hover) {
  background-color: var(--el-color-warning-light-9);
  & + .vxe-body--expanded-row {
    .expand-container {
      box-shadow:
        inset 4px 4px 0 0 var(--el-color-warning-light-9),
        /* 外阴影，右下 */ inset -4px -4px 0 0 var(--el-color-warning-light-9); /* 内阴影，左上 */
    }
  }
}

:deep(.vxe-cell) {
  height: 2.5rem !important;
}
</style>
