<template>
  <ContentWrap>
    <div class="h-[calc(100vh-170px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" plain @click="databaseFormRef?.openForm()"> 新增 </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          :header-cell-config="{ height: 40 }"
          :cell-config="{ height: 40 }"
          :data="list"
          border
          stripe
          show-overflow
          align="center"
          @cell-click="(el)=>databaseFormRef?.openForm(el.row.id)"
        >
          <vxe-column title="知识产权搜索词" align="left" field="word" min-width="240" />
          <vxe-column
            title="开始时间"
            min-width="100"
            :formatter="dateFormatter4"
            field="startDate"
          />
          <vxe-column
            title="结束时间"
            min-width="100"
            :formatter="dateFormatter4"
            field="endDate"
          />
          <vxe-column title="专利号" min-width="150" field="patentNo" />
          <vxe-column title="公开号" min-width="150" field="publicNo" />
          <vxe-column title="专利名称" min-width="150" field="patentName" />
          <vxe-column title="专利类型" field="patentType" min-width="100">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.PATENT_TYPE" :value="row.patentType" />
            </template>
          </vxe-column>
          <vxe-column title="权力人" min-width="120" field="ownership" />
          <vxe-column title="发明人" min-width="120" field="inventor" />
          <vxe-column
            title="申请日期"
            min-width="120"
            :formatter="dateFormatter4"
            field="applicationDate"
          />
          <vxe-column
            title="授权日期"
            min-width="120"
            :formatter="dateFormatter4"
            field="authorizationDate"
          />
          <vxe-column title="同族专利" min-width="120" field="families" />
          <vxe-column title="法律状态" field="legalStatus" min-width="90">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.LEGAL_STATUS" :value="row.legalStatus" />
            </template>
          </vxe-column>
          <vxe-column title="摘要" min-width="120" field="abstracts" />
          <vxe-column title="保护的整机类型" min-width="120" field="machineType" />
          <vxe-column title="保护的组件类型" min-width="120" field="component" />
          <vxe-column title="保护的零件类型" min-width="120" field="part" />
          <vxe-column title="技术点" min-width="120" field="technologyPoint" />
          <vxe-column title="解决的问题" min-width="120" field="solvedProblem" />
          <vxe-column title="功能点" min-width="120" field="functionPoint" />
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <DatabaseForm ref="databaseFormRef" @success="getList()" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { DatabaseApi, DatabaseVO } from '@/api/patent/database'
import DatabaseForm from './DatabaseForm.vue'
import { ref, onMounted, nextTick } from 'vue'
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter4, dateFormatter3 } from '@/utils/formatTime'

// 消息弹窗
const message = useMessage()
const databaseFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<DatabaseVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  patentType: undefined,
  legalStatus: undefined,
  word: undefined
})
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const data = await DatabaseApi.getDatabasePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await DatabaseApi.deleteDatabase(id)
  message.success('删除成功')
  // 刷新列表并保持滚动位置
  await getList()
}

onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
