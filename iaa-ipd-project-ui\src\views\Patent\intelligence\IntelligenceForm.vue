<template>
  <DatabaseDialog ref="databaseDialogRef" @success="handleSuccess" />
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      style="margin-top: 20px;"
    >
      <el-row :gutter="30">
        <el-col :span="12">
          <el-form-item label="专利号" prop="patentNo">
            <el-input v-model="formData.patentNo" placeholder="请选择知识产权数据库" readonly @click="handleOpenDatabaseDialog" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="专利名称" prop="patentName">
            <el-input v-model="formData.patentName" :readonly="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="24">
          <el-form-item label="关注的技术点" prop="follow">
            <el-input
              v-model="formData.follow"
              type="textarea"
              :rows="5"
              placeholder="请输入信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { IntelligenceApi } from '@/api/patent/intelligence'
import  DatabaseDialog  from './DatabaseDialog.vue'

const databaseDialogRef = ref()

const visible = ref(false)
const formData = ref({
  id: undefined,
  dataId: undefined,
  patentNo: undefined,
  patentName: undefined,
  follow: undefined
})
const formRules = reactive({
  patentNo: [{ required: true, message: '请选择专利号', trigger: 'blur' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const databaseDialog = ref(false)

const handleOpenDatabaseDialog = () => {
  if (databaseDialogRef.value) {
    databaseDialogRef.value.openDialog()
  } else {
    console.warn('DatabaseDialog 尚未加载')
  }
}

const openForm = async(rows?: any[] | any) => {
  visible.value = true
  if (rows) {
    formType.value = 'update'
    dialogTitle.value = '修改知识产权情报'
    const data = await IntelligenceApi.getIntelligence(rows)
    formData.value = {
      ...data
    }
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加知识产权情报'
    resetForm()
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    dataId: undefined,
    patentNo: undefined,
    patentName: undefined,
    follow: undefined
  }
}
//选择某一条知识产权后
const handleSuccess = async (rows: any) => { 
  formData.value.patentNo = rows.patentNo
  formData.value.patentName = rows.patentName
  formData.value.dataId = rows.id
}
const emit = defineEmits(['success']) 
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  console.log(formData.value)
  try {
    if (formType.value === 'update') {
      await IntelligenceApi.updateIntelligence(formData.value)
      message.success('修改成功')
    } else {
      await IntelligenceApi.createIntelligence(formData.value)
      message.success('创建成功')
    }
    visible.value = false
      emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
.tags-input-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 36px;

  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-right: 8px;
  }

  .tag {
    background-color: #f0f2f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e6e8eb;
    }

    .el-icon-close {
      font-size: 12px;
    }
  }

  .tag-input {
    border: none;
    outline: none;
    flex: 1;
    padding: 4px 0;
    font-size: 13px;
  }
}
</style>
