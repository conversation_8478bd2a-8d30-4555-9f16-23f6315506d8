import request from '@/config/axios'

export interface IntelligenceVO {
  id: any // 主键
  dataId: any // 数据ID
  patentNo: any // 专利号
  patentName: any // 专利名称
  follow: any // 我司关注的技术点
}

export const IntelligenceApi = {
  /** 分页获取知知识产权情报 */
  getIntelligencePage: (data: any) => {
    return request.post({ url: '/patent/intelligence/page', data })
  },
  /** 创建知识知识产权情报 */
  createIntelligence: (data: IntelligenceVO) => {
    return request.post({ url: '/patent/intelligence/create', data })
  },
  /** 更新知识知识产权情报 */
  updateIntelligence: (data: IntelligenceVO) => {
    return request.post({ url: '/patent/intelligence/update', data })
  },
  /** 删除知识知识产权情报 */
  deleteIntelligence: (id: number) => {
    return request.get({ url: `/patent/intelligence/delete/${id}`})
  },
  /** 查询知识产权情报详情 */
  getIntelligence: async (id: any) => {
    return await request.get({ url: `/patent/intelligence/get?id=` + id })
  },
}