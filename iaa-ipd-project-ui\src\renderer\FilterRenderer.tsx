// main.js

import { VxeUI } from 'vxe-table'
import TextFilter from '@/components/Filter/TextFilter.vue'
import UserFilter from '@/components/Filter/UserFilter.vue'
import UserOrDeptFilter from '@/components/Filter/UserOrDeptFilter.vue'
import { useUserStore } from '@/store/modules/user'

// 创建一个简单的输入框筛选渲染器
VxeUI.renderer.add('TextFilter', {
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <TextFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = ''
    })
  },
  // 自定义重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  tableFilterRecoverMethod ({ option }) {
    option.data = ''
  },
  // 自定义筛选方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    if (cellValue) {
      return cellValue.indexOf(data) > -1
    }
    return false
  }
})


VxeUI.renderer.add('UserFilter',{
  // 不显示底部按钮，使用自定义的按钮
  showTableFilterFooter: false,
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <UserFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'my', userList: [] }
    })
  },
  // 自定义筛选数据方法
  tableFilterMethod (params) {
    const {getUser}= useUserStore()
    const { option, row, column } = params
    const cellValue = row[column.field]
    const { userList, type } = option.data
    if (cellValue) {
      if (type === 'my') {
        return cellValue.includes(getUser.id)
      }
      // 将过滤值转为 Set，提升查找效率
      const filterSet = new Set(userList);
      
      // 判断 cellValue 是否包含 filterSet 中的任意一个元素
      return cellValue.some(val => filterSet.has(val));
    }
    return false
  }
})

VxeUI.renderer.add('UserOrDeptFilter',{
  // 不显示底部按钮，使用自定义的按钮
  showTableFilterFooter: false,
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <UserOrDeptFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'dept', deptList: [] }
    })
  },
  // 自定义筛选数据方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const cellValue = row[column.field]
    const { userList,deptList, type } = option.data
    if (cellValue) {
      // 将过滤值转为 Set，提升查找效率
      const filterSet = new Set(type=='user'?userList:deptList);
      
      // 判断 cellValue 是否包含 filterSet 中的任意一个元素
      return cellValue.some(val => filterSet.has(val));
    }
    return false
  }
})